<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 用户信息卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="4dp"
            app:cardCornerRadius="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <!-- 头像和基本信息 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <ImageView
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:src="@drawable/ic_profile"
                        android:background="@drawable/bg_avatar"
                        android:padding="16dp"
                        android:layout_marginEnd="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_user_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="?android:attr/textColorPrimary"
                            tools:text="张三" />

                        <TextView
                            android:id="@+id/tv_username"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="14sp"
                            android:textColor="?android:attr/textColorSecondary"
                            tools:text="@zhangsan" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_online_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        android:textColor="@color/success_color"
                        android:background="@drawable/bg_status_chip"
                        android:padding="6dp"
                        tools:text="在线" />

                </LinearLayout>

                <!-- 详细信息 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="角色"
                        android:textSize="12sp"
                        android:textColor="?android:attr/textColorSecondary" />

                    <TextView
                        android:id="@+id/tv_user_role"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="16sp"
                        android:textColor="?android:attr/textColorPrimary"
                        android:layout_marginBottom="8dp"
                        tools:text="单兵" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="队伍"
                        android:textSize="12sp"
                        android:textColor="?android:attr/textColorSecondary" />

                    <TextView
                        android:id="@+id/tv_team_id"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="16sp"
                        android:textColor="?android:attr/textColorPrimary"
                        android:layout_marginBottom="8dp"
                        tools:text="救援队001" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="设备ID"
                        android:textSize="12sp"
                        android:textColor="?android:attr/textColorSecondary" />

                    <TextView
                        android:id="@+id/tv_device_id"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="14sp"
                        android:textColor="?android:attr/textColorPrimary"
                        android:fontFamily="monospace"
                        tools:text="abc123def456" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 位置同步设置 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="4dp"
            app:cardCornerRadius="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="位置同步设置"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="?android:attr/textColorPrimary"
                    android:layout_marginBottom="16dp" />

                <!-- 自动同步开关 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="自动同步"
                        android:textSize="16sp"
                        android:textColor="?android:attr/textColorPrimary" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switch_location_sync"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <!-- 同步间隔设置 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="同步间隔"
                        android:textSize="16sp"
                        android:textColor="?android:attr/textColorPrimary" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_sync_interval"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="14sp"
                        style="@style/Widget.Material3.Button.OutlinedButton">

                        <TextView
                            android:id="@+id/tv_sync_interval"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="5分钟" />

                    </com.google.android.material.button.MaterialButton>

                </LinearLayout>

                <!-- 手动同步按钮 -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_manual_sync"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="立即同步"
                    android:visibility="gone"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 其他设置 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="4dp"
            app:cardCornerRadius="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="其他设置"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="?android:attr/textColorPrimary"
                    android:layout_marginBottom="16dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_clear_cache"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="清除缓存"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_logout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/logout"
                    android:backgroundTint="@color/error_color" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
