<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 百度地图容器 -->
    <com.baidu.mapapi.map.MapView
        android:id="@+id/map_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="true" />

    <!-- 控制面板 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardElevation="8dp"
        app:cardCornerRadius="12dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 位置同步模式 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <TextView
                    android:id="@+id/tv_sync_mode"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/auto_sync"
                    android:textSize="14sp" />

                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/switch_sync_mode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

            </LinearLayout>

            <!-- 手动同步按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_sync_now"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/sync_now"
                android:visibility="gone"
                style="@style/Widget.Material3.Button.OutlinedButton" />

            <!-- 显示轨迹开关 -->
            <com.google.android.material.button.MaterialButtonToggleGroup
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                app:singleSelection="true">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_show_track"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/show_track"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

            </com.google.android.material.button.MaterialButtonToggleGroup>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 位置信息显示 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_margin="16dp"
        app:cardElevation="8dp"
        app:cardCornerRadius="12dp">

        <TextView
            android:id="@+id/tv_location_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp"
            android:text="位置信息加载中..."
            android:textSize="12sp"
            android:fontFamily="monospace" />

    </com.google.android.material.card.MaterialCardView>

    <!-- 定位按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_my_location"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|bottom"
        android:layout_margin="16dp"
        android:layout_marginBottom="80dp"
        android:src="@drawable/ic_my_location"
        android:contentDescription="@string/my_location" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
