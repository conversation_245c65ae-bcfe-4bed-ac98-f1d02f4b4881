<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 筛选器 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            app:cardElevation="4dp"
            app:cardCornerRadius="8dp">

            <com.google.android.material.chip.ChipGroup
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="12dp"
                app:singleSelection="true"
                app:selectionRequired="false">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_all"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/all_missions"
                    android:checked="true"
                    style="@style/Widget.Material3.Chip.Filter" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_pending"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/pending_missions"
                    style="@style/Widget.Material3.Chip.Filter" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_in_progress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/in_progress_missions"
                    style="@style/Widget.Material3.Chip.Filter" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_completed"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/completed_missions"
                    style="@style/Widget.Material3.Chip.Filter" />

            </com.google.android.material.chip.ChipGroup>

        </com.google.android.material.card.MaterialCardView>

        <!-- 任务列表 -->
        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipe_refresh_layout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_view_missions"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="8dp"
                    android:clipToPadding="false" />

                <!-- 空状态 -->
                <LinearLayout
                    android:id="@+id/tv_empty_state"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:src="@drawable/ic_mission"
                        android:alpha="0.5"
                        android:layout_marginBottom="16dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="暂无任务"
                        android:textSize="16sp"
                        android:textColor="?android:attr/textColorSecondary" />

                </LinearLayout>

            </FrameLayout>

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    </LinearLayout>

    <!-- 创建任务按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_create_mission"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_add"
        android:contentDescription="@string/create_mission" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
