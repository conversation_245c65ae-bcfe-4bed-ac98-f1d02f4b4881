<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardElevation="4dp"
    app:cardCornerRadius="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 标题和状态 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_mission_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="?android:attr/textColorPrimary"
                tools:text="搜救任务001" />

            <TextView
                android:id="@+id/tv_mission_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textStyle="bold"
                android:padding="4dp"
                android:background="@drawable/bg_status_chip"
                tools:text="待接收" />

        </LinearLayout>

        <!-- 类型和优先级 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_mission_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="?android:attr/textColorSecondary"
                android:background="@drawable/bg_type_chip"
                android:padding="4dp"
                android:layout_marginEnd="8dp"
                tools:text="搜救" />

            <TextView
                android:id="@+id/tv_mission_priority"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textStyle="bold"
                android:padding="4dp"
                tools:text="高" />

        </LinearLayout>

        <!-- 描述 -->
        <TextView
            android:id="@+id/tv_mission_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="?android:attr/textColorPrimary"
            android:layout_marginBottom="8dp"
            android:maxLines="2"
            android:ellipsize="end"
            tools:text="在XX区域发现被困人员，需要立即进行搜救行动" />

        <!-- 位置信息 -->
        <TextView
            android:id="@+id/tv_mission_location"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:textColor="?android:attr/textColorSecondary"
            android:drawableStart="@drawable/ic_location"
            android:drawablePadding="4dp"
            android:layout_marginBottom="4dp"
            tools:text="目标: 39.9042, 116.4074" />

        <!-- 预计时长 -->
        <TextView
            android:id="@+id/tv_mission_duration"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:textColor="?android:attr/textColorSecondary"
            android:drawableStart="@drawable/ic_time"
            android:drawablePadding="4dp"
            android:layout_marginBottom="8dp"
            tools:text="预计: 120分钟" />

        <!-- 创建者和时间 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <TextView
                android:id="@+id/tv_mission_creator"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="12sp"
                android:textColor="?android:attr/textColorSecondary"
                tools:text="创建者: 张三" />

            <TextView
                android:id="@+id/tv_mission_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="?android:attr/textColorSecondary"
                tools:text="12-25 14:30" />

        </LinearLayout>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_accept"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/accept_mission"
                android:textSize="12sp"
                android:layout_marginEnd="8dp"
                style="@style/Widget.Material3.Button.OutlinedButton" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_update_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/update_status"
                android:textSize="12sp"
                style="@style/Widget.Material3.Button" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
