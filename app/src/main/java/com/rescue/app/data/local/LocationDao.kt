package com.rescue.app.data.local

import androidx.lifecycle.LiveData
import androidx.room.*
import com.rescue.app.domain.model.LocationData

@Dao
interface LocationDao {
    
    @Query("SELECT * FROM locations WHERE userId = :userId ORDER BY timestamp DESC")
    fun getLocationsByUser(userId: String): LiveData<List<LocationData>>
    
    @Query("SELECT * FROM locations WHERE userId = :userId AND timestamp BETWEEN :startTime AND :endTime ORDER BY timestamp ASC")
    fun getLocationsByUserAndTimeRange(userId: String, startTime: Long, endTime: Long): LiveData<List<LocationData>>
    
    @Query("SELECT * FROM locations WHERE isSynced = 0 ORDER BY timestamp ASC")
    suspend fun getUnsyncedLocations(): List<LocationData>
    
    @Query("SELECT * FROM locations WHERE userId = :userId ORDER BY timestamp DESC LIMIT 1")
    suspend fun getLatestLocation(userId: String): LocationData?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertLocation(location: LocationData): Long
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertLocations(locations: List<LocationData>)
    
    @Update
    suspend fun updateLocation(location: LocationData)
    
    @Query("UPDATE locations SET isSynced = 1 WHERE id IN (:locationIds)")
    suspend fun markLocationsSynced(locationIds: List<Long>)
    
    @Query("DELETE FROM locations WHERE userId = :userId AND timestamp < :beforeTime")
    suspend fun deleteOldLocations(userId: String, beforeTime: Long)
    
    @Query("DELETE FROM locations WHERE id = :locationId")
    suspend fun deleteLocation(locationId: Long)
    
    @Query("SELECT COUNT(*) FROM locations WHERE userId = :userId")
    suspend fun getLocationCount(userId: String): Int
}
