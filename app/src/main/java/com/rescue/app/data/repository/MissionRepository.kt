package com.rescue.app.data.repository

import androidx.lifecycle.LiveData
import com.rescue.app.data.local.MissionDao
import com.rescue.app.data.remote.ApiService
import com.rescue.app.domain.model.*
import com.rescue.app.utils.PreferenceManager
import com.rescue.app.utils.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

class MissionRepository(
    private val missionDao: MissionDao,
    private val apiService: ApiService,
    private val preferenceManager: PreferenceManager
) {
    
    fun getAllMissions(): LiveData<List<Mission>> {
        return missionDao.getAllMissions()
    }
    
    fun getMissionById(missionId: String): LiveData<Mission?> {
        return missionDao.getMissionByIdLiveData(missionId)
    }
    
    fun getMissionsByStatus(status: MissionStatus): LiveData<List<Mission>> {
        return missionDao.getMissionsByStatus(status)
    }
    
    fun getMissionsByAssignedUser(userId: String): LiveData<List<Mission>> {
        return missionDao.getMissionsByAssignedUser(userId)
    }
    
    fun getActiveMissions(): LiveData<List<Mission>> {
        val activeStatuses = listOf(
            MissionStatus.PENDING,
            MissionStatus.ACCEPTED,
            MissionStatus.IN_PROGRESS
        )
        return missionDao.getActiveMissions(activeStatuses)
    }
    
    suspend fun refreshMissions(): Flow<Resource<List<Mission>>> = flow {
        try {
            emit(Resource.Loading())
            
            val token = preferenceManager.getAuthToken()
            if (token == null) {
                emit(Resource.Error("用户未登录"))
                return@flow
            }
            
            val response = apiService.getMissions("Bearer $token")
            if (response.isSuccessful) {
                val missions = response.body() ?: emptyList()
                missionDao.insertMissions(missions)
                emit(Resource.Success(missions))
            } else {
                emit(Resource.Error("获取任务列表失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            emit(Resource.Error("获取任务列表异常: ${e.message}"))
        }
    }
    
    suspend fun createMission(missionRequest: MissionRequest): Flow<Resource<Mission>> = flow {
        try {
            emit(Resource.Loading())
            
            val token = preferenceManager.getAuthToken()
            if (token == null) {
                emit(Resource.Error("用户未登录"))
                return@flow
            }
            
            val response = apiService.createMission("Bearer $token", missionRequest)
            if (response.isSuccessful) {
                val missionResponse = response.body()
                if (missionResponse?.success == true && missionResponse.mission != null) {
                    missionDao.insertMission(missionResponse.mission)
                    emit(Resource.Success(missionResponse.mission))
                } else {
                    emit(Resource.Error(missionResponse?.message ?: "创建任务失败"))
                }
            } else {
                emit(Resource.Error("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            emit(Resource.Error("创建任务异常: ${e.message}"))
        }
    }
    
    suspend fun acceptMission(missionId: String): Flow<Resource<Boolean>> = flow {
        try {
            emit(Resource.Loading())
            
            val token = preferenceManager.getAuthToken()
            if (token == null) {
                emit(Resource.Error("用户未登录"))
                return@flow
            }
            
            val response = apiService.acceptMission("Bearer $token", missionId)
            if (response.isSuccessful) {
                val apiResponse = response.body()
                if (apiResponse?.success == true) {
                    missionDao.updateMissionStatus(missionId, MissionStatus.ACCEPTED)
                    emit(Resource.Success(true))
                } else {
                    emit(Resource.Error(apiResponse?.message ?: "接收任务失败"))
                }
            } else {
                emit(Resource.Error("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            emit(Resource.Error("接收任务异常: ${e.message}"))
        }
    }
    
    suspend fun updateMissionStatus(
        missionId: String, 
        status: MissionStatus, 
        notes: String? = null,
        currentLatitude: Double? = null,
        currentLongitude: Double? = null
    ): Flow<Resource<Boolean>> = flow {
        try {
            emit(Resource.Loading())
            
            val token = preferenceManager.getAuthToken()
            if (token == null) {
                emit(Resource.Error("用户未登录"))
                return@flow
            }
            
            val statusUpdate = MissionStatusUpdate(missionId, status, notes, currentLatitude, currentLongitude)
            val response = apiService.updateMissionStatus("Bearer $token", missionId, statusUpdate)
            
            if (response.isSuccessful) {
                val apiResponse = response.body()
                if (apiResponse?.success == true) {
                    missionDao.updateMissionStatus(missionId, status)
                    emit(Resource.Success(true))
                } else {
                    emit(Resource.Error(apiResponse?.message ?: "更新任务状态失败"))
                }
            } else {
                emit(Resource.Error("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            emit(Resource.Error("更新任务状态异常: ${e.message}"))
        }
    }
    
    suspend fun getMissionCountByStatus(status: MissionStatus): Int {
        return missionDao.getMissionCountByStatus(status)
    }
}
