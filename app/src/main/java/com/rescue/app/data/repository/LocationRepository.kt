package com.rescue.app.data.repository

import androidx.lifecycle.LiveData
import com.rescue.app.data.local.LocationDao
import com.rescue.app.data.remote.ApiService
import com.rescue.app.domain.model.*
import com.rescue.app.utils.PreferenceManager
import com.rescue.app.utils.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

class LocationRepository(
    private val locationDao: LocationDao,
    private val apiService: ApiService,
    private val preferenceManager: PreferenceManager
) {
    
    fun getLocationsByUser(userId: String): LiveData<List<LocationData>> {
        return locationDao.getLocationsByUser(userId)
    }
    
    fun getLocationsByUserAndTimeRange(userId: String, startTime: Long, endTime: Long): LiveData<List<LocationData>> {
        return locationDao.getLocationsByUserAndTimeRange(userId, startTime, endTime)
    }
    
    suspend fun insertLocation(location: LocationData): Long {
        return locationDao.insertLocation(location)
    }
    
    suspend fun getLatestLocation(userId: String): LocationData? {
        return locationDao.getLatestLocation(userId)
    }
    
    suspend fun syncLocations(): Flow<Resource<LocationSyncResponse>> = flow {
        try {
            emit(Resource.Loading())
            
            val token = preferenceManager.getAuthToken()
            val user = preferenceManager.getUser()
            
            if (token == null || user == null) {
                emit(Resource.Error("用户未登录"))
                return@flow
            }
            
            val unsyncedLocations = locationDao.getUnsyncedLocations()
            if (unsyncedLocations.isEmpty()) {
                emit(Resource.Success(LocationSyncResponse(true, "没有需要同步的位置数据", 0)))
                return@flow
            }
            
            val request = LocationSyncRequest(user.id, unsyncedLocations)
            val response = apiService.syncLocations("Bearer $token", request)
            
            if (response.isSuccessful) {
                val syncResponse = response.body()
                if (syncResponse?.success == true) {
                    // 标记为已同步
                    val locationIds = unsyncedLocations.map { it.id }
                    locationDao.markLocationsSynced(locationIds)
                    emit(Resource.Success(syncResponse))
                } else {
                    emit(Resource.Error(syncResponse?.message ?: "同步失败"))
                }
            } else {
                emit(Resource.Error("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            emit(Resource.Error("同步异常: ${e.message}"))
        }
    }
    
    suspend fun getTeamLocations(teamId: String): Flow<Resource<List<TeamMemberLocation>>> = flow {
        try {
            emit(Resource.Loading())
            
            val token = preferenceManager.getAuthToken()
            if (token == null) {
                emit(Resource.Error("用户未登录"))
                return@flow
            }
            
            val response = apiService.getTeamLocations("Bearer $token", teamId)
            if (response.isSuccessful) {
                val locations = response.body() ?: emptyList()
                emit(Resource.Success(locations))
            } else {
                emit(Resource.Error("获取队友位置失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            emit(Resource.Error("获取队友位置异常: ${e.message}"))
        }
    }
    
    suspend fun getUserLocationHistory(userId: String, startTime: Long, endTime: Long): Flow<Resource<List<LocationData>>> = flow {
        try {
            emit(Resource.Loading())
            
            val token = preferenceManager.getAuthToken()
            if (token == null) {
                emit(Resource.Error("用户未登录"))
                return@flow
            }
            
            val response = apiService.getUserLocationHistory("Bearer $token", userId, startTime, endTime)
            if (response.isSuccessful) {
                val locations = response.body() ?: emptyList()
                emit(Resource.Success(locations))
            } else {
                emit(Resource.Error("获取历史轨迹失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            emit(Resource.Error("获取历史轨迹异常: ${e.message}"))
        }
    }
    
    suspend fun deleteOldLocations(userId: String, beforeTime: Long) {
        locationDao.deleteOldLocations(userId, beforeTime)
    }
    
    suspend fun getLocationCount(userId: String): Int {
        return locationDao.getLocationCount(userId)
    }
    
    fun getLocationSyncMode(): LocationSyncMode {
        return preferenceManager.getLocationSyncMode()
    }
    
    fun setLocationSyncMode(mode: LocationSyncMode) {
        preferenceManager.setLocationSyncMode(mode)
    }
    
    fun getLocationSyncInterval(): Long {
        return preferenceManager.getLocationSyncInterval()
    }
    
    fun setLocationSyncInterval(intervalMinutes: Long) {
        preferenceManager.setLocationSyncInterval(intervalMinutes)
    }
}
