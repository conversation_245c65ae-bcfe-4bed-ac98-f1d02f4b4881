package com.rescue.app.data.local

import androidx.lifecycle.LiveData
import androidx.room.*
import com.rescue.app.domain.model.Mission
import com.rescue.app.domain.model.MissionStatus

@Dao
interface MissionDao {
    
    @Query("SELECT * FROM missions ORDER BY createdTime DESC")
    fun getAllMissions(): LiveData<List<Mission>>
    
    @Query("SELECT * FROM missions WHERE id = :missionId")
    suspend fun getMissionById(missionId: String): Mission?
    
    @Query("SELECT * FROM missions WHERE id = :missionId")
    fun getMissionByIdLiveData(missionId: String): LiveData<Mission?>
    
    @Query("SELECT * FROM missions WHERE status = :status ORDER BY createdTime DESC")
    fun getMissionsByStatus(status: MissionStatus): LiveData<List<Mission>>
    
    @Query("SELECT * FROM missions WHERE assignedUserIds LIKE '%' || :userId || '%' ORDER BY createdTime DESC")
    fun getMissionsByAssignedUser(userId: String): LiveData<List<Mission>>
    
    @Query("SELECT * FROM missions WHERE creatorId = :userId ORDER BY createdTime DESC")
    fun getMissionsByCreator(userId: String): LiveData<List<Mission>>
    
    @Query("SELECT * FROM missions WHERE status IN (:statuses) ORDER BY createdTime DESC")
    fun getActiveMissions(statuses: List<MissionStatus>): LiveData<List<Mission>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMission(mission: Mission)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMissions(missions: List<Mission>)
    
    @Update
    suspend fun updateMission(mission: Mission)
    
    @Query("UPDATE missions SET status = :status WHERE id = :missionId")
    suspend fun updateMissionStatus(missionId: String, status: MissionStatus)
    
    @Delete
    suspend fun deleteMission(mission: Mission)
    
    @Query("DELETE FROM missions WHERE id = :missionId")
    suspend fun deleteMissionById(missionId: String)
    
    @Query("SELECT COUNT(*) FROM missions WHERE status = :status")
    suspend fun getMissionCountByStatus(status: MissionStatus): Int
}
