package com.rescue.app.data.repository

import com.rescue.app.data.remote.ApiService
import com.rescue.app.domain.model.LoginRequest
import com.rescue.app.domain.model.LoginResponse
import com.rescue.app.domain.model.User
import com.rescue.app.utils.PreferenceManager
import com.rescue.app.utils.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

class UserRepository(
    private val apiService: ApiService,
    private val preferenceManager: PreferenceManager
) {
    
    suspend fun login(username: String, password: String, deviceId: String): Flow<Resource<LoginResponse>> = flow {
        try {
            emit(Resource.Loading())
            
            val request = LoginRequest(username, password, deviceId)
            val response = apiService.login(request)
            
            if (response.isSuccessful) {
                val loginResponse = response.body()
                if (loginResponse?.success == true && loginResponse.token != null && loginResponse.user != null) {
                    // 保存登录信息
                    preferenceManager.saveAuthToken(loginResponse.token)
                    preferenceManager.saveUser(loginResponse.user)
                    preferenceManager.setLoggedIn(true)
                    
                    emit(Resource.Success(loginResponse))
                } else {
                    emit(Resource.Error(loginResponse?.message ?: "登录失败"))
                }
            } else {
                emit(Resource.Error("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            emit(Resource.Error("登录异常: ${e.message}"))
        }
    }
    
    suspend fun logout(): Flow<Resource<Boolean>> = flow {
        try {
            emit(Resource.Loading())
            
            val token = preferenceManager.getAuthToken()
            if (token != null) {
                val response = apiService.logout("Bearer $token")
                if (response.isSuccessful) {
                    clearUserData()
                    emit(Resource.Success(true))
                } else {
                    // 即使服务器返回错误，也清除本地数据
                    clearUserData()
                    emit(Resource.Success(true))
                }
            } else {
                clearUserData()
                emit(Resource.Success(true))
            }
        } catch (e: Exception) {
            // 即使网络异常，也清除本地数据
            clearUserData()
            emit(Resource.Success(true))
        }
    }
    
    suspend fun getUserProfile(): Flow<Resource<User>> = flow {
        try {
            emit(Resource.Loading())
            
            val token = preferenceManager.getAuthToken()
            if (token != null) {
                val response = apiService.getUserProfile("Bearer $token")
                if (response.isSuccessful) {
                    val user = response.body()
                    if (user != null) {
                        preferenceManager.saveUser(user)
                        emit(Resource.Success(user))
                    } else {
                        emit(Resource.Error("用户信息为空"))
                    }
                } else {
                    emit(Resource.Error("获取用户信息失败: ${response.code()}"))
                }
            } else {
                emit(Resource.Error("未登录"))
            }
        } catch (e: Exception) {
            emit(Resource.Error("获取用户信息异常: ${e.message}"))
        }
    }
    
    fun getCurrentUser(): User? {
        return preferenceManager.getUser()
    }
    
    fun isLoggedIn(): Boolean {
        return preferenceManager.isLoggedIn() && preferenceManager.getAuthToken() != null
    }
    
    fun getAuthToken(): String? {
        return preferenceManager.getAuthToken()
    }
    
    private fun clearUserData() {
        preferenceManager.clearAuthToken()
        preferenceManager.clearUser()
        preferenceManager.setLoggedIn(false)
    }
}
