package com.rescue.app.data.local

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.rescue.app.domain.model.LocationData
import com.rescue.app.domain.model.Mission

@Database(
    entities = [LocationData::class, Mission::class],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class RescueDatabase : RoomDatabase() {
    
    abstract fun locationDao(): LocationDao
    abstract fun missionDao(): MissionDao
    
    companion object {
        @Volatile
        private var INSTANCE: RescueDatabase? = null
        
        fun getDatabase(context: Context): RescueDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    RescueDatabase::class.java,
                    "rescue_database"
                )
                .fallbackToDestructiveMigration()
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
