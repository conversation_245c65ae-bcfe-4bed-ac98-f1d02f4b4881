package com.rescue.app.data.remote

import com.rescue.app.domain.model.*
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.*
import java.util.concurrent.TimeUnit

interface ApiService {
    
    // 用户认证相关
    @POST("auth/login")
    suspend fun login(@Body request: LoginRequest): Response<LoginResponse>
    
    @POST("auth/logout")
    suspend fun logout(@Header("Authorization") token: String): Response<ApiResponse>
    
    @GET("auth/profile")
    suspend fun getUserProfile(@Header("Authorization") token: String): Response<User>
    
    // 位置同步相关
    @POST("location/sync")
    suspend fun syncLocations(
        @Header("Authorization") token: String,
        @Body request: LocationSyncRequest
    ): Response<LocationSyncResponse>
    
    @GET("location/team/{teamId}")
    suspend fun getTeamLocations(
        @Header("Authorization") token: String,
        @Path("teamId") teamId: String
    ): Response<List<TeamMemberLocation>>
    
    @GET("location/user/{userId}/history")
    suspend fun getUserLocationHistory(
        @Header("Authorization") token: String,
        @Path("userId") userId: String,
        @Query("startTime") startTime: Long,
        @Query("endTime") endTime: Long
    ): Response<List<LocationData>>
    
    // 任务管理相关
    @GET("missions")
    suspend fun getMissions(
        @Header("Authorization") token: String,
        @Query("status") status: String? = null
    ): Response<List<Mission>>
    
    @GET("missions/{missionId}")
    suspend fun getMissionById(
        @Header("Authorization") token: String,
        @Path("missionId") missionId: String
    ): Response<Mission>
    
    @POST("missions")
    suspend fun createMission(
        @Header("Authorization") token: String,
        @Body request: MissionRequest
    ): Response<MissionResponse>
    
    @PUT("missions/{missionId}/status")
    suspend fun updateMissionStatus(
        @Header("Authorization") token: String,
        @Path("missionId") missionId: String,
        @Body statusUpdate: MissionStatusUpdate
    ): Response<ApiResponse>
    
    @PUT("missions/{missionId}/accept")
    suspend fun acceptMission(
        @Header("Authorization") token: String,
        @Path("missionId") missionId: String
    ): Response<ApiResponse>
    
    companion object {
        private const val BASE_URL = "http://your-server-url/api/"
        
        fun create(): ApiService {
            val loggingInterceptor = HttpLoggingInterceptor().apply {
                level = HttpLoggingInterceptor.Level.BODY
            }
            
            val client = OkHttpClient.Builder()
                .addInterceptor(loggingInterceptor)
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build()
            
            return Retrofit.Builder()
                .baseUrl(BASE_URL)
                .client(client)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
                .create(ApiService::class.java)
        }
    }
}

data class ApiResponse(
    val success: Boolean,
    val message: String,
    val data: Any? = null
)
