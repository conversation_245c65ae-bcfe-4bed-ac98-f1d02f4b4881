package com.rescue.app.data.local

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.rescue.app.domain.model.*

class Converters {
    
    private val gson = Gson()
    
    @TypeConverter
    fun fromStringList(value: List<String>): String {
        return gson.toJson(value)
    }
    
    @TypeConverter
    fun toStringList(value: String): List<String> {
        val listType = object : TypeToken<List<String>>() {}.type
        return gson.fromJson(value, listType) ?: emptyList()
    }
    
    @TypeConverter
    fun fromUserRole(role: UserRole): String {
        return role.name
    }
    
    @TypeConverter
    fun toUserRole(role: String): UserRole {
        return UserRole.valueOf(role)
    }
    
    @TypeConverter
    fun fromMissionType(type: MissionType): String {
        return type.name
    }
    
    @TypeConverter
    fun toMissionType(type: String): MissionType {
        return MissionType.valueOf(type)
    }
    
    @TypeConverter
    fun fromMissionPriority(priority: MissionPriority): String {
        return priority.name
    }
    
    @TypeConverter
    fun toMissionPriority(priority: String): MissionPriority {
        return MissionPriority.valueOf(priority)
    }
    
    @TypeConverter
    fun fromMissionStatus(status: MissionStatus): String {
        return status.name
    }
    
    @TypeConverter
    fun toMissionStatus(status: String): MissionStatus {
        return MissionStatus.valueOf(status)
    }
    
    @TypeConverter
    fun fromLocationSyncMode(mode: LocationSyncMode): String {
        return mode.name
    }
    
    @TypeConverter
    fun toLocationSyncMode(mode: String): LocationSyncMode {
        return LocationSyncMode.valueOf(mode)
    }
}
