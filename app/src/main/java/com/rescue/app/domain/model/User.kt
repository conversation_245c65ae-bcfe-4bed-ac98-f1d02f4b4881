package com.rescue.app.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class User(
    val id: String,
    val username: String,
    val name: String,
    val role: UserRole,
    val teamId: String?,
    val deviceId: String,
    val isOnline: Boolean = false,
    val lastActiveTime: Long = System.currentTimeMillis()
) : Parcelable

enum class UserRole {
    SOLDIER,      // 单兵
    COMMANDER,    // 指挥官
    ADMIN         // 管理员
}

@Parcelize
data class LoginRequest(
    val username: String,
    val password: String,
    val deviceId: String
) : Parcelable

@Parcelize
data class LoginResponse(
    val success: Boolean,
    val message: String,
    val token: String?,
    val user: User?
) : Parcelable
