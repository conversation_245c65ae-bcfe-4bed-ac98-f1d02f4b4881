package com.rescue.app.domain.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize

@Entity(tableName = "missions")
@Parcelize
data class Mission(
    @PrimaryKey
    val id: String,
    val title: String,
    val description: String,
    val type: MissionType,
    val priority: MissionPriority,
    val status: MissionStatus,
    val creatorId: String,
    val creatorName: String,
    val assignedUserIds: List<String> = emptyList(),
    val targetLatitude: Double?,
    val targetLongitude: Double?,
    val targetAddress: String?,
    val createdTime: Long = System.currentTimeMillis(),
    val startTime: Long? = null,
    val endTime: Long? = null,
    val estimatedDuration: Long? = null, // 预计持续时间（分钟）
    val notes: String? = null
) : Parcelable

enum class MissionType {
    SEARCH_RESCUE,    // 搜救任务
    PATROL,           // 巡逻任务
    EMERGENCY,        // 紧急任务
    TRAINING          // 训练任务
}

enum class MissionPriority {
    LOW,
    MEDIUM,
    HIGH,
    URGENT
}

enum class MissionStatus {
    PENDING,      // 待接收
    ACCEPTED,     // 已接收
    IN_PROGRESS,  // 进行中
    COMPLETED,    // 已完成
    CANCELLED,    // 已取消
    FAILED        // 失败
}

@Parcelize
data class MissionRequest(
    val title: String,
    val description: String,
    val type: MissionType,
    val priority: MissionPriority,
    val targetLatitude: Double?,
    val targetLongitude: Double?,
    val targetAddress: String?,
    val assignedUserIds: List<String> = emptyList(),
    val estimatedDuration: Long? = null
) : Parcelable

@Parcelize
data class MissionResponse(
    val success: Boolean,
    val message: String,
    val mission: Mission?
) : Parcelable

@Parcelize
data class MissionStatusUpdate(
    val missionId: String,
    val status: MissionStatus,
    val notes: String? = null,
    val currentLatitude: Double? = null,
    val currentLongitude: Double? = null
) : Parcelable
