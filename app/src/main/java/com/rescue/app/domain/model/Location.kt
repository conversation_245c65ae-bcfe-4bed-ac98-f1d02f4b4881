package com.rescue.app.domain.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize

@Entity(tableName = "locations")
@Parcelize
data class LocationData(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val userId: String,
    val latitude: Double,
    val longitude: Double,
    val altitude: Double = 0.0,
    val accuracy: Float = 0f,
    val speed: Float = 0f,
    val bearing: Float = 0f,
    val timestamp: Long = System.currentTimeMillis(),
    val isSynced: Boolean = false
) : Parcelable

@Parcelize
data class TeamMemberLocation(
    val userId: String,
    val username: String,
    val name: String,
    val latitude: Double,
    val longitude: Double,
    val timestamp: Long,
    val isOnline: Boolean,
    val batteryLevel: Int? = null
) : Parcelable

@Parcelize
data class LocationSyncRequest(
    val userId: String,
    val locations: List<LocationData>
) : Parcelable

@Parcelize
data class LocationSyncResponse(
    val success: Boolean,
    val message: String,
    val syncedCount: Int
) : Parcelable

enum class LocationSyncMode {
    AUTO,    // 自动同步
    MANUAL   // 手动同步
}
