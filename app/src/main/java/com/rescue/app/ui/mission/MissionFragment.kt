package com.rescue.app.ui.mission

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.rescue.app.databinding.FragmentMissionBinding
import com.rescue.app.domain.model.Mission
import com.rescue.app.domain.model.MissionStatus
import com.rescue.app.utils.Resource
import kotlinx.coroutines.launch

class MissionFragment : Fragment() {
    
    private var _binding: FragmentMissionBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: MissionViewModel by viewModels()
    private lateinit var missionAdapter: MissionAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentMissionBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupRecyclerView()
        setupUI()
        observeViewModel()
        
        // 加载任务数据
        viewModel.loadMissions()
    }
    
    private fun setupRecyclerView() {
        missionAdapter = MissionAdapter(
            onMissionClick = { mission ->
                showMissionDetails(mission)
            },
            onAcceptClick = { mission ->
                showAcceptMissionDialog(mission)
            },
            onStatusUpdateClick = { mission ->
                showStatusUpdateDialog(mission)
            }
        )
        
        binding.recyclerViewMissions.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = missionAdapter
        }
    }
    
    private fun setupUI() {
        // 刷新按钮
        binding.swipeRefreshLayout.setOnRefreshListener {
            viewModel.refreshMissions()
        }
        
        // 创建任务按钮
        binding.fabCreateMission.setOnClickListener {
            showCreateMissionDialog()
        }
        
        // 筛选按钮
        binding.chipAll.setOnClickListener {
            viewModel.filterMissions(null)
        }
        
        binding.chipPending.setOnClickListener {
            viewModel.filterMissions(MissionStatus.PENDING)
        }
        
        binding.chipInProgress.setOnClickListener {
            viewModel.filterMissions(MissionStatus.IN_PROGRESS)
        }
        
        binding.chipCompleted.setOnClickListener {
            viewModel.filterMissions(MissionStatus.COMPLETED)
        }
    }
    
    private fun observeViewModel() {
        // 观察任务列表
        viewModel.missions.observe(viewLifecycleOwner) { missions ->
            missionAdapter.submitList(missions)
            binding.tvEmptyState.visibility = if (missions.isEmpty()) View.VISIBLE else View.GONE
        }
        
        // 观察刷新状态
        lifecycleScope.launch {
            viewModel.refreshState.collect { resource ->
                binding.swipeRefreshLayout.isRefreshing = resource is Resource.Loading
                
                when (resource) {
                    is Resource.Success -> {
                        // 刷新成功
                    }
                    is Resource.Error -> {
                        Toast.makeText(context, "刷新失败: ${resource.message}", Toast.LENGTH_SHORT).show()
                    }
                    is Resource.Loading -> {
                        // 显示加载状态
                    }
                    null -> {
                        // 初始状态
                    }
                }
            }
        }
        
        // 观察任务操作状态
        lifecycleScope.launch {
            viewModel.missionActionState.collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        Toast.makeText(context, "操作成功", Toast.LENGTH_SHORT).show()
                    }
                    is Resource.Error -> {
                        Toast.makeText(context, "操作失败: ${resource.message}", Toast.LENGTH_SHORT).show()
                    }
                    is Resource.Loading -> {
                        // 显示加载状态
                    }
                    null -> {
                        // 初始状态
                    }
                }
            }
        }
    }
    
    private fun showMissionDetails(mission: Mission) {
        val details = buildString {
            append("任务ID: ${mission.id}\n")
            append("标题: ${mission.title}\n")
            append("描述: ${mission.description}\n")
            append("类型: ${getMissionTypeText(mission.type)}\n")
            append("优先级: ${getMissionPriorityText(mission.priority)}\n")
            append("状态: ${getMissionStatusText(mission.status)}\n")
            append("创建者: ${mission.creatorName}\n")
            append("创建时间: ${formatTime(mission.createdTime)}\n")
            
            if (mission.targetLatitude != null && mission.targetLongitude != null) {
                append("目标位置: ${mission.targetLatitude}, ${mission.targetLongitude}\n")
            }
            
            if (mission.targetAddress != null) {
                append("目标地址: ${mission.targetAddress}\n")
            }
            
            if (mission.estimatedDuration != null) {
                append("预计时长: ${mission.estimatedDuration}分钟\n")
            }
            
            if (mission.notes != null) {
                append("备注: ${mission.notes}")
            }
        }
        
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("任务详情")
            .setMessage(details)
            .setPositiveButton("确定", null)
            .show()
    }
    
    private fun showAcceptMissionDialog(mission: Mission) {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("接收任务")
            .setMessage("确定要接收任务「${mission.title}」吗？")
            .setPositiveButton("接收") { _, _ ->
                viewModel.acceptMission(mission.id)
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    private fun showStatusUpdateDialog(mission: Mission) {
        val statusOptions = arrayOf("开始执行", "完成任务", "取消任务")
        val statusValues = arrayOf(MissionStatus.IN_PROGRESS, MissionStatus.COMPLETED, MissionStatus.CANCELLED)
        
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("更新任务状态")
            .setItems(statusOptions) { _, which ->
                viewModel.updateMissionStatus(mission.id, statusValues[which])
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    private fun showCreateMissionDialog() {
        // TODO: 实现创建任务对话框
        Toast.makeText(context, "创建任务功能开发中...", Toast.LENGTH_SHORT).show()
    }
    
    private fun getMissionTypeText(type: com.rescue.app.domain.model.MissionType): String {
        return when (type) {
            com.rescue.app.domain.model.MissionType.SEARCH_RESCUE -> "搜救任务"
            com.rescue.app.domain.model.MissionType.PATROL -> "巡逻任务"
            com.rescue.app.domain.model.MissionType.EMERGENCY -> "紧急任务"
            com.rescue.app.domain.model.MissionType.TRAINING -> "训练任务"
        }
    }
    
    private fun getMissionPriorityText(priority: com.rescue.app.domain.model.MissionPriority): String {
        return when (priority) {
            com.rescue.app.domain.model.MissionPriority.LOW -> "低"
            com.rescue.app.domain.model.MissionPriority.MEDIUM -> "中"
            com.rescue.app.domain.model.MissionPriority.HIGH -> "高"
            com.rescue.app.domain.model.MissionPriority.URGENT -> "紧急"
        }
    }
    
    private fun getMissionStatusText(status: MissionStatus): String {
        return when (status) {
            MissionStatus.PENDING -> "待接收"
            MissionStatus.ACCEPTED -> "已接收"
            MissionStatus.IN_PROGRESS -> "进行中"
            MissionStatus.COMPLETED -> "已完成"
            MissionStatus.CANCELLED -> "已取消"
            MissionStatus.FAILED -> "失败"
        }
    }
    
    private fun formatTime(timestamp: Long): String {
        return java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
            .format(java.util.Date(timestamp))
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
