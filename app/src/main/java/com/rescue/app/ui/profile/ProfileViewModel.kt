package com.rescue.app.ui.profile

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.rescue.app.RescueApplication
import com.rescue.app.domain.model.LocationSyncMode
import com.rescue.app.domain.model.User
import com.rescue.app.service.LocationSyncService
import com.rescue.app.utils.Resource
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class ProfileViewModel : ViewModel() {
    
    private val app = RescueApplication.instance
    private val userRepository = app.userRepository
    private val locationRepository = app.locationRepository
    
    private val _userInfo = MutableLiveData<User?>()
    val userInfo: LiveData<User?> = _userInfo
    
    private val _locationSyncMode = MutableLiveData<LocationSyncMode>()
    val locationSyncMode: LiveData<LocationSyncMode> = _locationSyncMode
    
    private val _syncInterval = MutableLiveData<Long>()
    val syncInterval: LiveData<Long> = _syncInterval
    
    private val _logoutState = MutableStateFlow<Resource<Boolean>?>(null)
    val logoutState: StateFlow<Resource<Boolean>?> = _logoutState.asStateFlow()
    
    fun loadUserInfo() {
        val user = userRepository.getCurrentUser()
        _userInfo.value = user
    }
    
    fun loadLocationSettings() {
        _locationSyncMode.value = locationRepository.getLocationSyncMode()
        _syncInterval.value = locationRepository.getLocationSyncInterval()
    }
    
    fun setLocationSyncMode(mode: LocationSyncMode) {
        locationRepository.setLocationSyncMode(mode)
        _locationSyncMode.value = mode
        
        // 根据模式启动或停止位置服务
        val context = app.applicationContext
        when (mode) {
            LocationSyncMode.AUTO -> {
                LocationSyncService.startService(context)
            }
            LocationSyncMode.MANUAL -> {
                // 手动模式下仍然保持服务运行，但不自动同步
                LocationSyncService.startService(context)
            }
        }
    }
    
    fun setSyncInterval(intervalMinutes: Long) {
        locationRepository.setLocationSyncInterval(intervalMinutes)
        _syncInterval.value = intervalMinutes
    }
    
    fun logout() {
        viewModelScope.launch {
            // 停止位置服务
            LocationSyncService.stopService(app.applicationContext)
            
            // 执行退出登录
            userRepository.logout().collect { resource ->
                _logoutState.value = resource
            }
        }
    }
    
    fun clearCache() {
        viewModelScope.launch {
            try {
                val user = userRepository.getCurrentUser()
                if (user != null) {
                    // 清除旧的位置数据（保留最近1天的数据）
                    val oneDayAgo = System.currentTimeMillis() - (24 * 60 * 60 * 1000)
                    locationRepository.deleteOldLocations(user.id, oneDayAgo)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    fun refreshUserProfile() {
        viewModelScope.launch {
            userRepository.getUserProfile().collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        _userInfo.value = resource.data
                    }
                    is Resource.Error -> {
                        // 处理错误，可能需要重新登录
                    }
                    is Resource.Loading -> {
                        // 显示加载状态
                    }
                }
            }
        }
    }
}
