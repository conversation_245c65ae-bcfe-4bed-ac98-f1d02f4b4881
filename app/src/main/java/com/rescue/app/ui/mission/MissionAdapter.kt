package com.rescue.app.ui.mission

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.rescue.app.R
import com.rescue.app.databinding.ItemMissionBinding
import com.rescue.app.domain.model.Mission
import com.rescue.app.domain.model.MissionPriority
import com.rescue.app.domain.model.MissionStatus
import com.rescue.app.domain.model.MissionType
import java.text.SimpleDateFormat
import java.util.*

class MissionAdapter(
    private val onMissionClick: (Mission) -> Unit,
    private val onAcceptClick: (Mission) -> Unit,
    private val onStatusUpdateClick: (Mission) -> Unit
) : ListAdapter<Mission, MissionAdapter.MissionViewHolder>(MissionDiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MissionViewHolder {
        val binding = ItemMissionBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return MissionViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: MissionViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    inner class MissionViewHolder(
        private val binding: ItemMissionBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(mission: Mission) {
            binding.apply {
                // 基本信息
                tvMissionTitle.text = mission.title
                tvMissionDescription.text = mission.description
                tvMissionType.text = getMissionTypeText(mission.type)
                tvMissionCreator.text = "创建者: ${mission.creatorName}"
                tvMissionTime.text = formatTime(mission.createdTime)
                
                // 优先级
                tvMissionPriority.text = getMissionPriorityText(mission.priority)
                tvMissionPriority.setTextColor(getPriorityColor(mission.priority))
                
                // 状态
                tvMissionStatus.text = getMissionStatusText(mission.status)
                tvMissionStatus.setTextColor(getStatusColor(mission.status))
                
                // 目标位置
                if (mission.targetLatitude != null && mission.targetLongitude != null) {
                    tvMissionLocation.text = "目标: ${String.format("%.4f, %.4f", 
                        mission.targetLatitude, mission.targetLongitude)}"
                    tvMissionLocation.visibility = View.VISIBLE
                } else {
                    tvMissionLocation.visibility = View.GONE
                }
                
                // 预计时长
                if (mission.estimatedDuration != null) {
                    tvMissionDuration.text = "预计: ${mission.estimatedDuration}分钟"
                    tvMissionDuration.visibility = View.VISIBLE
                } else {
                    tvMissionDuration.visibility = View.GONE
                }
                
                // 按钮状态
                setupButtons(mission)
                
                // 点击事件
                root.setOnClickListener { onMissionClick(mission) }
                btnAccept.setOnClickListener { onAcceptClick(mission) }
                btnUpdateStatus.setOnClickListener { onStatusUpdateClick(mission) }
            }
        }
        
        private fun setupButtons(mission: Mission) {
            binding.apply {
                when (mission.status) {
                    MissionStatus.PENDING -> {
                        btnAccept.visibility = View.VISIBLE
                        btnUpdateStatus.visibility = View.GONE
                    }
                    MissionStatus.ACCEPTED, MissionStatus.IN_PROGRESS -> {
                        btnAccept.visibility = View.GONE
                        btnUpdateStatus.visibility = View.VISIBLE
                        btnUpdateStatus.text = "更新状态"
                    }
                    MissionStatus.COMPLETED, MissionStatus.CANCELLED, MissionStatus.FAILED -> {
                        btnAccept.visibility = View.GONE
                        btnUpdateStatus.visibility = View.GONE
                    }
                }
            }
        }
        
        private fun getMissionTypeText(type: MissionType): String {
            return when (type) {
                MissionType.SEARCH_RESCUE -> "搜救"
                MissionType.PATROL -> "巡逻"
                MissionType.EMERGENCY -> "紧急"
                MissionType.TRAINING -> "训练"
            }
        }
        
        private fun getMissionPriorityText(priority: MissionPriority): String {
            return when (priority) {
                MissionPriority.LOW -> "低"
                MissionPriority.MEDIUM -> "中"
                MissionPriority.HIGH -> "高"
                MissionPriority.URGENT -> "紧急"
            }
        }
        
        private fun getMissionStatusText(status: MissionStatus): String {
            return when (status) {
                MissionStatus.PENDING -> "待接收"
                MissionStatus.ACCEPTED -> "已接收"
                MissionStatus.IN_PROGRESS -> "进行中"
                MissionStatus.COMPLETED -> "已完成"
                MissionStatus.CANCELLED -> "已取消"
                MissionStatus.FAILED -> "失败"
            }
        }
        
        private fun getPriorityColor(priority: MissionPriority): Int {
            val context = binding.root.context
            return when (priority) {
                MissionPriority.LOW -> ContextCompat.getColor(context, R.color.priority_low)
                MissionPriority.MEDIUM -> ContextCompat.getColor(context, R.color.priority_medium)
                MissionPriority.HIGH -> ContextCompat.getColor(context, R.color.priority_high)
                MissionPriority.URGENT -> ContextCompat.getColor(context, R.color.priority_urgent)
            }
        }
        
        private fun getStatusColor(status: MissionStatus): Int {
            val context = binding.root.context
            return when (status) {
                MissionStatus.PENDING -> ContextCompat.getColor(context, R.color.warning_color)
                MissionStatus.ACCEPTED -> ContextCompat.getColor(context, R.color.info_color)
                MissionStatus.IN_PROGRESS -> ContextCompat.getColor(context, R.color.info_color)
                MissionStatus.COMPLETED -> ContextCompat.getColor(context, R.color.success_color)
                MissionStatus.CANCELLED, MissionStatus.FAILED -> ContextCompat.getColor(context, R.color.error_color)
            }
        }
        
        private fun formatTime(timestamp: Long): String {
            return SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())
                .format(Date(timestamp))
        }
    }
    
    class MissionDiffCallback : DiffUtil.ItemCallback<Mission>() {
        override fun areItemsTheSame(oldItem: Mission, newItem: Mission): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: Mission, newItem: Mission): Boolean {
            return oldItem == newItem
        }
    }
}
