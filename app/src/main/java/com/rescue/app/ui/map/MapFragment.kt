package com.rescue.app.ui.map

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.baidu.mapapi.map.*
import com.baidu.mapapi.model.LatLng
import com.rescue.app.R
import com.rescue.app.databinding.FragmentMapBinding
import com.rescue.app.domain.model.LocationData
import com.rescue.app.domain.model.LocationSyncMode
import com.rescue.app.domain.model.TeamMemberLocation
import com.rescue.app.service.LocationSyncService
import com.rescue.app.utils.BaiduMapUtils
import com.rescue.app.utils.Resource
import kotlinx.coroutines.launch

class MapFragment : Fragment() {

    private var _binding: FragmentMapBinding? = null
    private val binding get() = _binding!!

    private val viewModel: MapViewModel by viewModels()

    private var baiduMap: BaiduMap? = null
    private var mapView: MapView? = null
    private var currentLocationMarker: Marker? = null
    private val teamMemberMarkers = mutableMapOf<String, Marker>()
    private var currentLocationPolyline: Polyline? = null
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentMapBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupMap()
        setupUI()
        observeViewModel()
    }
    
    private fun setupMap() {
        val mapFragment = childFragmentManager.findFragmentById(R.id.map) as SupportMapFragment?
        mapFragment?.getMapAsync(this)
    }
    
    private fun setupUI() {
        // 位置同步模式切换
        binding.switchSyncMode.setOnCheckedChangeListener { _, isChecked ->
            val mode = if (isChecked) LocationSyncMode.AUTO else LocationSyncMode.MANUAL
            viewModel.setLocationSyncMode(mode)
            updateSyncModeUI(mode)
        }
        
        // 手动同步按钮
        binding.btnSyncNow.setOnClickListener {
            LocationSyncService.syncNow(requireContext())
            Toast.makeText(context, "正在同步位置信息...", Toast.LENGTH_SHORT).show()
        }
        
        // 显示轨迹按钮
        binding.btnShowTrack.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                viewModel.loadLocationHistory()
            } else {
                clearLocationTrack()
            }
        }
        
        // 定位到当前位置
        binding.fabMyLocation.setOnClickListener {
            viewModel.getCurrentLocation()
        }
        
        // 初始化UI状态
        val currentMode = viewModel.getLocationSyncMode()
        binding.switchSyncMode.isChecked = currentMode == LocationSyncMode.AUTO
        updateSyncModeUI(currentMode)
    }
    
    private fun observeViewModel() {
        // 观察当前位置
        viewModel.currentLocation.observe(viewLifecycleOwner) { location ->
            location?.let { updateCurrentLocationMarker(it) }
        }
        
        // 观察队友位置
        lifecycleScope.launch {
            viewModel.teamLocations.collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        updateTeamMemberMarkers(resource.data ?: emptyList())
                    }
                    is Resource.Error -> {
                        Toast.makeText(context, "获取队友位置失败: ${resource.message}", Toast.LENGTH_SHORT).show()
                    }
                    is Resource.Loading -> {
                        // 显示加载状态
                    }
                }
            }
        }
        
        // 观察历史轨迹
        lifecycleScope.launch {
            viewModel.locationHistory.collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        showLocationTrack(resource.data ?: emptyList())
                    }
                    is Resource.Error -> {
                        Toast.makeText(context, "获取历史轨迹失败: ${resource.message}", Toast.LENGTH_SHORT).show()
                    }
                    is Resource.Loading -> {
                        // 显示加载状态
                    }
                }
            }
        }
    }
    
    override fun onMapReady(map: GoogleMap) {
        googleMap = map
        
        // 配置地图
        map.apply {
            mapType = GoogleMap.MAP_TYPE_NORMAL
            uiSettings.isZoomControlsEnabled = true
            uiSettings.isCompassEnabled = true
            uiSettings.isMyLocationButtonEnabled = false // 使用自定义按钮
            
            // 尝试启用我的位置图层
            try {
                isMyLocationEnabled = true
            } catch (e: SecurityException) {
                // 权限不足
            }
        }
        
        // 加载数据
        viewModel.loadCurrentLocation()
        viewModel.loadTeamLocations()
    }
    
    private fun updateCurrentLocationMarker(location: LocationData) {
        val googleMap = this.googleMap ?: return
        
        val position = LatLng(location.latitude, location.longitude)
        
        if (currentLocationMarker == null) {
            currentLocationMarker = googleMap.addMarker(
                MarkerOptions()
                    .position(position)
                    .title("我的位置")
                    .icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_BLUE))
            )
            
            // 移动相机到当前位置
            googleMap.animateCamera(CameraUpdateFactory.newLatLngZoom(position, 15f))
        } else {
            currentLocationMarker?.position = position
        }
        
        // 更新位置信息显示
        binding.tvLocationInfo.text = "位置: ${String.format("%.6f, %.6f", location.latitude, location.longitude)}\n" +
                "精度: ${location.accuracy}m\n" +
                "时间: ${java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date(location.timestamp))}"
    }
    
    private fun updateTeamMemberMarkers(teamLocations: List<TeamMemberLocation>) {
        val googleMap = this.googleMap ?: return
        
        // 清除旧的标记
        teamMemberMarkers.values.forEach { it.remove() }
        teamMemberMarkers.clear()
        
        // 添加新的标记
        teamLocations.forEach { member ->
            val position = LatLng(member.latitude, member.longitude)
            val marker = googleMap.addMarker(
                MarkerOptions()
                    .position(position)
                    .title(member.name)
                    .snippet("${member.username} - ${if (member.isOnline) "在线" else "离线"}")
                    .icon(BitmapDescriptorFactory.defaultMarker(
                        if (member.isOnline) BitmapDescriptorFactory.HUE_GREEN 
                        else BitmapDescriptorFactory.HUE_RED
                    ))
            )
            
            marker?.let { teamMemberMarkers[member.userId] = it }
        }
    }
    
    private fun showLocationTrack(locations: List<LocationData>) {
        val googleMap = this.googleMap ?: return
        
        clearLocationTrack()
        
        if (locations.size < 2) return
        
        val points = locations.map { LatLng(it.latitude, it.longitude) }
        
        currentLocationPolyline = googleMap.addPolyline(
            PolylineOptions()
                .addAll(points)
                .color(resources.getColor(R.color.track_color, null))
                .width(8f)
                .pattern(listOf(Dash(20f), Gap(10f)))
        )
    }
    
    private fun clearLocationTrack() {
        currentLocationPolyline?.remove()
        currentLocationPolyline = null
    }
    
    private fun updateSyncModeUI(mode: LocationSyncMode) {
        binding.btnSyncNow.visibility = if (mode == LocationSyncMode.MANUAL) View.VISIBLE else View.GONE
        binding.tvSyncMode.text = when (mode) {
            LocationSyncMode.AUTO -> "自动同步模式"
            LocationSyncMode.MANUAL -> "手动同步模式"
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
