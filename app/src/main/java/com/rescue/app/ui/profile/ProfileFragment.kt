package com.rescue.app.ui.profile

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.rescue.app.databinding.FragmentProfileBinding
import com.rescue.app.domain.model.LocationSyncMode
import com.rescue.app.service.LocationSyncService
import com.rescue.app.ui.login.LoginActivity
import com.rescue.app.utils.Resource
import kotlinx.coroutines.launch

class ProfileFragment : Fragment() {
    
    private var _binding: FragmentProfileBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: ProfileViewModel by viewModels()
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentProfileBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupUI()
        observeViewModel()
        loadUserInfo()
    }
    
    private fun setupUI() {
        // 退出登录
        binding.btnLogout.setOnClickListener {
            showLogoutDialog()
        }
        
        // 位置同步设置
        binding.switchLocationSync.setOnCheckedChangeListener { _, isChecked ->
            val mode = if (isChecked) LocationSyncMode.AUTO else LocationSyncMode.MANUAL
            viewModel.setLocationSyncMode(mode)
        }
        
        // 同步间隔设置
        binding.btnSyncInterval.setOnClickListener {
            showSyncIntervalDialog()
        }
        
        // 手动同步
        binding.btnManualSync.setOnClickListener {
            LocationSyncService.syncNow(requireContext())
            Toast.makeText(context, "正在同步位置信息...", Toast.LENGTH_SHORT).show()
        }
        
        // 清除缓存
        binding.btnClearCache.setOnClickListener {
            showClearCacheDialog()
        }
    }
    
    private fun observeViewModel() {
        // 观察用户信息
        viewModel.userInfo.observe(viewLifecycleOwner) { user ->
            user?.let {
                binding.tvUserName.text = it.name
                binding.tvUsername.text = "@${it.username}"
                binding.tvUserRole.text = when (it.role) {
                    com.rescue.app.domain.model.UserRole.SOLDIER -> "单兵"
                    com.rescue.app.domain.model.UserRole.COMMANDER -> "指挥官"
                    com.rescue.app.domain.model.UserRole.ADMIN -> "管理员"
                }
                binding.tvTeamId.text = it.teamId ?: "未分配队伍"
                binding.tvDeviceId.text = it.deviceId
                binding.tvOnlineStatus.text = if (it.isOnline) "在线" else "离线"
            }
        }
        
        // 观察位置同步设置
        viewModel.locationSyncMode.observe(viewLifecycleOwner) { mode ->
            binding.switchLocationSync.isChecked = mode == LocationSyncMode.AUTO
            binding.btnManualSync.visibility = if (mode == LocationSyncMode.MANUAL) View.VISIBLE else View.GONE
        }
        
        // 观察同步间隔
        viewModel.syncInterval.observe(viewLifecycleOwner) { interval ->
            binding.tvSyncInterval.text = "${interval}分钟"
        }
        
        // 观察退出登录状态
        lifecycleScope.launch {
            viewModel.logoutState.collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        navigateToLogin()
                    }
                    is Resource.Error -> {
                        Toast.makeText(context, "退出登录失败: ${resource.message}", Toast.LENGTH_SHORT).show()
                    }
                    is Resource.Loading -> {
                        // 显示加载状态
                    }
                    null -> {
                        // 初始状态
                    }
                }
            }
        }
    }
    
    private fun loadUserInfo() {
        viewModel.loadUserInfo()
        viewModel.loadLocationSettings()
    }
    
    private fun showLogoutDialog() {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("退出登录")
            .setMessage("确定要退出登录吗？")
            .setPositiveButton("确定") { _, _ ->
                viewModel.logout()
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    private fun showSyncIntervalDialog() {
        val intervals = arrayOf("1分钟", "5分钟", "10分钟", "15分钟", "30分钟", "60分钟")
        val intervalValues = arrayOf(1L, 5L, 10L, 15L, 30L, 60L)
        
        val currentInterval = viewModel.syncInterval.value ?: 5L
        val currentIndex = intervalValues.indexOf(currentInterval).takeIf { it >= 0 } ?: 1
        
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("选择同步间隔")
            .setSingleChoiceItems(intervals, currentIndex) { dialog, which ->
                viewModel.setSyncInterval(intervalValues[which])
                dialog.dismiss()
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    private fun showClearCacheDialog() {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("清除缓存")
            .setMessage("这将清除本地存储的位置数据和任务信息，确定继续吗？")
            .setPositiveButton("确定") { _, _ ->
                viewModel.clearCache()
                Toast.makeText(context, "缓存已清除", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    private fun navigateToLogin() {
        val intent = Intent(requireContext(), LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        requireActivity().finish()
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
