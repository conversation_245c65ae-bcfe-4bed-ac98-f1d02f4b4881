package com.rescue.app.ui.map

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.baidu.mapapi.map.*
import com.baidu.mapapi.model.LatLng
import com.rescue.app.databinding.FragmentMapBinding
import com.rescue.app.domain.model.LocationData
import com.rescue.app.domain.model.LocationSyncMode
import com.rescue.app.domain.model.TeamMemberLocation
import com.rescue.app.service.LocationSyncService
import com.rescue.app.utils.BaiduMapUtils
import com.rescue.app.utils.Resource
import kotlinx.coroutines.launch

class BaiduMapFragment : Fragment() {
    
    private var _binding: FragmentMapBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: MapViewModel by viewModels()
    
    private var baiduMap: BaiduMap? = null
    private var mapView: MapView? = null
    private var currentLocationMarker: Marker? = null
    private val teamMemberMarkers = mutableMapOf<String, Marker>()
    private var currentLocationPolyline: Polyline? = null
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentMapBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupMap()
        setupUI()
        observeViewModel()
    }
    
    private fun setupMap() {
        mapView = binding.mapView
        baiduMap = mapView?.map
        
        // 配置地图
        baiduMap?.apply {
            mapType = BaiduMap.MAP_TYPE_NORMAL
            uiSettings.isZoomControlsEnabled = true
            uiSettings.isCompassEnabled = true
            isMyLocationEnabled = true
            
            // 设置地图状态监听
            setOnMapStatusChangeListener(object : BaiduMap.OnMapStatusChangeListener {
                override fun onMapStatusChangeStart(status: MapStatus?) {}
                override fun onMapStatusChangeStart(status: MapStatus?, reason: Int) {}
                override fun onMapStatusChange(status: MapStatus?) {}
                override fun onMapStatusChangeFinish(status: MapStatus?) {}
            })
        }
        
        // 加载数据
        viewModel.loadCurrentLocation()
        viewModel.loadTeamLocations()
    }
    
    private fun setupUI() {
        // 位置同步模式切换
        binding.switchSyncMode.setOnCheckedChangeListener { _, isChecked ->
            val mode = if (isChecked) LocationSyncMode.AUTO else LocationSyncMode.MANUAL
            viewModel.setLocationSyncMode(mode)
            updateSyncModeUI(mode)
        }
        
        // 手动同步按钮
        binding.btnSyncNow.setOnClickListener {
            LocationSyncService.syncNow(requireContext())
            Toast.makeText(context, "正在同步位置信息...", Toast.LENGTH_SHORT).show()
        }
        
        // 显示轨迹按钮
        binding.btnShowTrack.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                viewModel.loadLocationHistory()
            } else {
                clearLocationTrack()
            }
        }
        
        // 定位到当前位置
        binding.fabMyLocation.setOnClickListener {
            viewModel.getCurrentLocation()
        }
        
        // 初始化UI状态
        val currentMode = viewModel.getLocationSyncMode()
        binding.switchSyncMode.isChecked = currentMode == LocationSyncMode.AUTO
        updateSyncModeUI(currentMode)
    }
    
    private fun observeViewModel() {
        // 观察当前位置
        viewModel.currentLocation.observe(viewLifecycleOwner) { location ->
            location?.let { updateCurrentLocationMarker(it) }
        }
        
        // 观察队友位置
        lifecycleScope.launch {
            viewModel.teamLocations.collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        updateTeamMemberMarkers(resource.data ?: emptyList())
                    }
                    is Resource.Error -> {
                        Toast.makeText(context, "获取队友位置失败: ${resource.message}", Toast.LENGTH_SHORT).show()
                    }
                    is Resource.Loading -> {
                        // 显示加载状态
                    }
                }
            }
        }
        
        // 观察历史轨迹
        lifecycleScope.launch {
            viewModel.locationHistory.collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        showLocationTrack(resource.data ?: emptyList())
                    }
                    is Resource.Error -> {
                        Toast.makeText(context, "获取历史轨迹失败: ${resource.message}", Toast.LENGTH_SHORT).show()
                    }
                    is Resource.Loading -> {
                        // 显示加载状态
                    }
                }
            }
        }
    }
    
    private fun updateCurrentLocationMarker(location: LocationData) {
        val baiduMap = this.baiduMap ?: return
        
        val position = LatLng(location.latitude, location.longitude)
        
        if (currentLocationMarker == null) {
            val markerOptions = BaiduMapUtils.createCurrentLocationMarker(location)
            currentLocationMarker = baiduMap.addOverlay(markerOptions) as Marker
            
            // 移动相机到当前位置
            BaiduMapUtils.moveMapToLocation(baiduMap, location.latitude, location.longitude, 15f)
        } else {
            currentLocationMarker?.position = position
        }
        
        // 更新位置信息显示
        binding.tvLocationInfo.text = "位置: ${String.format("%.6f, %.6f", location.latitude, location.longitude)}\n" +
                "精度: ${location.accuracy}m\n" +
                "时间: ${java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date(location.timestamp))}"
    }
    
    private fun updateTeamMemberMarkers(teamLocations: List<TeamMemberLocation>) {
        val baiduMap = this.baiduMap ?: return
        
        // 清除旧的标记
        teamMemberMarkers.values.forEach { it.remove() }
        teamMemberMarkers.clear()
        
        // 添加新的标记
        teamLocations.forEach { member ->
            val markerOptions = BaiduMapUtils.createTeamMemberMarker(member)
            val marker = baiduMap.addOverlay(markerOptions) as Marker
            teamMemberMarkers[member.userId] = marker
        }
    }
    
    private fun showLocationTrack(locations: List<LocationData>) {
        val baiduMap = this.baiduMap ?: return
        
        clearLocationTrack()
        
        if (locations.size < 2) return
        
        val polylineOptions = BaiduMapUtils.createPolylineOptions(locations)
        currentLocationPolyline = baiduMap.addOverlay(polylineOptions) as Polyline
    }
    
    private fun clearLocationTrack() {
        currentLocationPolyline?.remove()
        currentLocationPolyline = null
    }
    
    private fun updateSyncModeUI(mode: LocationSyncMode) {
        binding.btnSyncNow.visibility = if (mode == LocationSyncMode.MANUAL) View.VISIBLE else View.GONE
        binding.tvSyncMode.text = when (mode) {
            LocationSyncMode.AUTO -> "自动同步模式"
            LocationSyncMode.MANUAL -> "手动同步模式"
        }
    }
    
    override fun onResume() {
        super.onResume()
        mapView?.onResume()
    }
    
    override fun onPause() {
        super.onPause()
        mapView?.onPause()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        mapView?.onDestroy()
        _binding = null
    }
}
