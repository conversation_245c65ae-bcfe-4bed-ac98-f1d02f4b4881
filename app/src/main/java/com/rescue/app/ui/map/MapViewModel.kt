package com.rescue.app.ui.map

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.rescue.app.RescueApplication
import com.rescue.app.domain.model.LocationData
import com.rescue.app.domain.model.LocationSyncMode
import com.rescue.app.domain.model.TeamMemberLocation
import com.rescue.app.utils.Resource
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.*

class MapViewModel : ViewModel() {
    
    private val app = RescueApplication.instance
    private val locationRepository = app.locationRepository
    private val userRepository = app.userRepository
    
    private val _currentLocation = MutableLiveData<LocationData?>()
    val currentLocation: LiveData<LocationData?> = _currentLocation
    
    private val _teamLocations = MutableStateFlow<Resource<List<TeamMemberLocation>>?>(null)
    val teamLocations: StateFlow<Resource<List<TeamMemberLocation>>?> = _teamLocations.asStateFlow()
    
    private val _locationHistory = MutableStateFlow<Resource<List<LocationData>>?>(null)
    val locationHistory: StateFlow<Resource<List<LocationData>>?> = _locationHistory.asStateFlow()
    
    fun loadCurrentLocation() {
        val user = userRepository.getCurrentUser() ?: return
        
        viewModelScope.launch {
            try {
                val location = locationRepository.getLatestLocation(user.id)
                _currentLocation.value = location
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        
        // 观察位置变化
        locationRepository.getLocationsByUser(user.id).observeForever { locations ->
            if (locations.isNotEmpty()) {
                _currentLocation.value = locations.first()
            }
        }
    }
    
    fun getCurrentLocation() {
        loadCurrentLocation()
    }
    
    fun loadTeamLocations() {
        val user = userRepository.getCurrentUser()
        if (user?.teamId == null) return
        
        viewModelScope.launch {
            locationRepository.getTeamLocations(user.teamId).collect { resource ->
                _teamLocations.value = resource
            }
        }
    }
    
    fun loadLocationHistory() {
        val user = userRepository.getCurrentUser() ?: return
        
        viewModelScope.launch {
            // 获取最近24小时的轨迹
            val endTime = System.currentTimeMillis()
            val startTime = endTime - (24 * 60 * 60 * 1000) // 24小时前
            
            locationRepository.getUserLocationHistory(user.id, startTime, endTime).collect { resource ->
                _locationHistory.value = resource
            }
        }
    }
    
    fun setLocationSyncMode(mode: LocationSyncMode) {
        locationRepository.setLocationSyncMode(mode)
    }
    
    fun getLocationSyncMode(): LocationSyncMode {
        return locationRepository.getLocationSyncMode()
    }
    
    fun setLocationSyncInterval(intervalMinutes: Long) {
        locationRepository.setLocationSyncInterval(intervalMinutes)
    }
    
    fun getLocationSyncInterval(): Long {
        return locationRepository.getLocationSyncInterval()
    }
}
