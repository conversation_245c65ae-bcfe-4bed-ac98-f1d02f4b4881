package com.rescue.app.ui.login

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.rescue.app.RescueApplication
import com.rescue.app.domain.model.LoginResponse
import com.rescue.app.utils.Resource
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class LoginViewModel : ViewModel() {
    
    private val userRepository = RescueApplication.instance.userRepository
    
    private val _loginState = MutableStateFlow<Resource<LoginResponse>?>(null)
    val loginState: StateFlow<Resource<LoginResponse>?> = _loginState.asStateFlow()
    
    fun login(username: String, password: String, deviceId: String) {
        viewModelScope.launch {
            userRepository.login(username, password, deviceId).collect { resource ->
                _loginState.value = resource
            }
        }
    }
}
