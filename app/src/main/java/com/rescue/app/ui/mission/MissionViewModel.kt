package com.rescue.app.ui.mission

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.rescue.app.RescueApplication
import com.rescue.app.domain.model.Mission
import com.rescue.app.domain.model.MissionStatus
import com.rescue.app.utils.Resource
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class MissionViewModel : ViewModel() {
    
    private val app = RescueApplication.instance
    private val missionRepository = app.missionRepository
    private val userRepository = app.userRepository
    
    private val _missions = MutableLiveData<List<Mission>>()
    val missions: LiveData<List<Mission>> = _missions
    
    private val _refreshState = MutableStateFlow<Resource<List<Mission>>?>(null)
    val refreshState: StateFlow<Resource<List<Mission>>?> = _refreshState.asStateFlow()
    
    private val _missionActionState = MutableStateFlow<Resource<Boolean>?>(null)
    val missionActionState: StateFlow<Resource<Boolean>?> = _missionActionState.asStateFlow()
    
    private var allMissions: List<Mission> = emptyList()
    private var currentFilter: MissionStatus? = null
    
    init {
        loadMissions()
    }
    
    fun loadMissions() {
        val user = userRepository.getCurrentUser() ?: return
        
        // 观察本地数据库中的任务
        missionRepository.getAllMissions().observeForever { missions ->
            allMissions = missions
            applyFilter()
        }
        
        // 同时观察分配给当前用户的任务
        missionRepository.getMissionsByAssignedUser(user.id).observeForever { assignedMissions ->
            // 可以在这里处理分配给当前用户的任务
        }
    }
    
    fun refreshMissions() {
        viewModelScope.launch {
            missionRepository.refreshMissions().collect { resource ->
                _refreshState.value = resource
            }
        }
    }
    
    fun filterMissions(status: MissionStatus?) {
        currentFilter = status
        applyFilter()
    }
    
    private fun applyFilter() {
        val filteredMissions = if (currentFilter == null) {
            allMissions
        } else {
            allMissions.filter { it.status == currentFilter }
        }
        _missions.value = filteredMissions
    }
    
    fun acceptMission(missionId: String) {
        viewModelScope.launch {
            missionRepository.acceptMission(missionId).collect { resource ->
                _missionActionState.value = resource
            }
        }
    }
    
    fun updateMissionStatus(missionId: String, status: MissionStatus, notes: String? = null) {
        viewModelScope.launch {
            // 获取当前位置信息
            val user = userRepository.getCurrentUser()
            if (user != null) {
                val locationRepository = app.locationRepository
                val currentLocation = locationRepository.getLatestLocation(user.id)
                
                missionRepository.updateMissionStatus(
                    missionId = missionId,
                    status = status,
                    notes = notes,
                    currentLatitude = currentLocation?.latitude,
                    currentLongitude = currentLocation?.longitude
                ).collect { resource ->
                    _missionActionState.value = resource
                }
            }
        }
    }
    
    fun getActiveMissions(): LiveData<List<Mission>> {
        return missionRepository.getActiveMissions()
    }
    
    fun getMissionsByStatus(status: MissionStatus): LiveData<List<Mission>> {
        return missionRepository.getMissionsByStatus(status)
    }
}
