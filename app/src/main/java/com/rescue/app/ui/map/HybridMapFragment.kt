package com.rescue.app.ui.map

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.amap.api.maps.AMap
import com.amap.api.maps.MapView
import com.rescue.app.databinding.FragmentHybridMapBinding
import com.rescue.app.domain.model.LocationData
import com.rescue.app.domain.model.LocationSyncMode
import com.rescue.app.domain.model.TeamMemberLocation
import com.rescue.app.service.LocationSyncService
import com.rescue.app.utils.AmapOfflineUtils
import com.rescue.app.utils.NetworkUtils
import com.rescue.app.utils.Resource
import kotlinx.coroutines.launch
import org.osmdroid.views.overlay.Marker
import org.osmdroid.views.overlay.Polyline

class HybridMapFragment : Fragment() {
    
    private var _binding: FragmentHybridMapBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: MapViewModel by viewModels()
    
    // 高德地图相关
    private var amapView: MapView? = null
    private var amap: AMap? = null
    private var amapCurrentLocationMarker: com.amap.api.maps.model.Marker? = null
    private val amapTeamMemberMarkers = mutableMapOf<String, com.amap.api.maps.model.Marker>()
    private var amapTrackPolyline: com.amap.api.maps.model.Polyline? = null
    
    // OSM离线地图相关
    private var osmMapView: org.osmdroid.views.MapView? = null
    private var osmCurrentLocationMarker: Marker? = null
    private val osmTeamMemberMarkers = mutableMapOf<String, Marker>()
    private var osmTrackPolyline: Polyline? = null
    
    private var isOnlineMode = true
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHybridMapBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupMaps(savedInstanceState)
        setupUI()
        observeViewModel()
        checkNetworkAndSwitchMap()
    }
    
    private fun setupMaps(savedInstanceState: Bundle?) {
        // 初始化高德地图
        amapView = binding.amapView
        amapView?.onCreate(savedInstanceState)
        amap = amapView?.map
        
        // 配置高德地图
        amap?.apply {
            mapType = AMap.MAP_TYPE_NORMAL
            uiSettings.isZoomControlsEnabled = true
            uiSettings.isCompassEnabled = true
            isMyLocationEnabled = true
        }
        
        // 初始化OSM离线地图
        osmMapView = binding.osmMapView
        AmapOfflineUtils.setupOSMMapView(osmMapView!!, requireContext())
        
        // 加载数据
        viewModel.loadCurrentLocation()
        viewModel.loadTeamLocations()
    }
    
    private fun setupUI() {
        // 地图模式切换
        binding.switchMapMode.setOnCheckedChangeListener { _, isChecked ->
            switchMapMode(isChecked)
        }
        
        // 位置同步模式切换
        binding.switchSyncMode.setOnCheckedChangeListener { _, isChecked ->
            val mode = if (isChecked) LocationSyncMode.AUTO else LocationSyncMode.MANUAL
            viewModel.setLocationSyncMode(mode)
            updateSyncModeUI(mode)
        }
        
        // 手动同步按钮
        binding.btnSyncNow.setOnClickListener {
            LocationSyncService.syncNow(requireContext())
            Toast.makeText(context, "正在同步位置信息...", Toast.LENGTH_SHORT).show()
        }
        
        // 显示轨迹按钮
        binding.btnShowTrack.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                viewModel.loadLocationHistory()
            } else {
                clearLocationTrack()
            }
        }
        
        // 定位到当前位置
        binding.fabMyLocation.setOnClickListener {
            viewModel.getCurrentLocation()
        }
        
        // 下载离线地图
        binding.btnDownloadOfflineMap.setOnClickListener {
            downloadOfflineMap()
        }
        
        // 初始化UI状态
        val currentMode = viewModel.getLocationSyncMode()
        binding.switchSyncMode.isChecked = currentMode == LocationSyncMode.AUTO
        updateSyncModeUI(currentMode)
    }
    
    private fun observeViewModel() {
        // 观察当前位置
        viewModel.currentLocation.observe(viewLifecycleOwner) { location ->
            location?.let { updateCurrentLocationMarker(it) }
        }
        
        // 观察队友位置
        lifecycleScope.launch {
            viewModel.teamLocations.collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        updateTeamMemberMarkers(resource.data ?: emptyList())
                    }
                    is Resource.Error -> {
                        Toast.makeText(context, "获取队友位置失败: ${resource.message}", Toast.LENGTH_SHORT).show()
                    }
                    is Resource.Loading -> {
                        // 显示加载状态
                    }
                }
            }
        }
        
        // 观察历史轨迹
        lifecycleScope.launch {
            viewModel.locationHistory.collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        showLocationTrack(resource.data ?: emptyList())
                    }
                    is Resource.Error -> {
                        Toast.makeText(context, "获取历史轨迹失败: ${resource.message}", Toast.LENGTH_SHORT).show()
                    }
                    is Resource.Loading -> {
                        // 显示加载状态
                    }
                }
            }
        }
    }
    
    private fun checkNetworkAndSwitchMap() {
        isOnlineMode = NetworkUtils.isNetworkAvailable(requireContext())
        switchMapMode(isOnlineMode)
        binding.switchMapMode.isChecked = isOnlineMode
        
        // 更新UI提示
        binding.tvMapMode.text = if (isOnlineMode) "在线地图模式" else "离线地图模式"
    }
    
    private fun switchMapMode(online: Boolean) {
        isOnlineMode = online
        
        if (online) {
            // 切换到高德在线地图
            binding.amapView.visibility = View.VISIBLE
            binding.osmMapView.visibility = View.GONE
            binding.tvMapMode.text = "在线地图模式"
        } else {
            // 切换到OSM离线地图
            binding.amapView.visibility = View.GONE
            binding.osmMapView.visibility = View.VISIBLE
            binding.tvMapMode.text = "离线地图模式"
        }
        
        // 重新加载当前数据
        viewModel.getCurrentLocation()
    }
    
    private fun updateCurrentLocationMarker(location: LocationData) {
        if (isOnlineMode) {
            updateAmapCurrentLocationMarker(location)
        } else {
            updateOsmCurrentLocationMarker(location)
        }
        
        // 更新位置信息显示
        binding.tvLocationInfo.text = "位置: ${String.format("%.6f, %.6f", location.latitude, location.longitude)}\n" +
                "精度: ${location.accuracy}m\n" +
                "时间: ${java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date(location.timestamp))}"
    }
    
    private fun updateAmapCurrentLocationMarker(location: LocationData) {
        val amap = this.amap ?: return
        
        if (amapCurrentLocationMarker == null) {
            amapCurrentLocationMarker = AmapOfflineUtils.addAmapCurrentLocationMarker(amap, location, requireContext())
            AmapOfflineUtils.moveAmapToLocation(amap, location.latitude, location.longitude, 15f)
        } else {
            amapCurrentLocationMarker?.position = com.amap.api.maps.model.LatLng(location.latitude, location.longitude)
        }
    }
    
    private fun updateOsmCurrentLocationMarker(location: LocationData) {
        val osmMapView = this.osmMapView ?: return
        
        if (osmCurrentLocationMarker == null) {
            osmCurrentLocationMarker = AmapOfflineUtils.addCurrentLocationMarker(osmMapView, location, requireContext())
            AmapOfflineUtils.moveMapToLocation(osmMapView, location.latitude, location.longitude, 15.0)
        } else {
            osmCurrentLocationMarker?.position = org.osmdroid.util.GeoPoint(location.latitude, location.longitude)
        }
        osmMapView.invalidate()
    }
    
    private fun updateTeamMemberMarkers(teamLocations: List<TeamMemberLocation>) {
        if (isOnlineMode) {
            updateAmapTeamMemberMarkers(teamLocations)
        } else {
            updateOsmTeamMemberMarkers(teamLocations)
        }
    }
    
    private fun updateAmapTeamMemberMarkers(teamLocations: List<TeamMemberLocation>) {
        val amap = this.amap ?: return
        
        // 清除旧的标记
        amapTeamMemberMarkers.values.forEach { it.remove() }
        amapTeamMemberMarkers.clear()
        
        // 添加新的标记
        teamLocations.forEach { member ->
            val marker = AmapOfflineUtils.addAmapTeamMemberMarker(amap, member)
            amapTeamMemberMarkers[member.userId] = marker
        }
    }
    
    private fun updateOsmTeamMemberMarkers(teamLocations: List<TeamMemberLocation>) {
        val osmMapView = this.osmMapView ?: return
        
        // 清除旧的标记
        osmTeamMemberMarkers.values.forEach { osmMapView.overlays.remove(it) }
        osmTeamMemberMarkers.clear()
        
        // 添加新的标记
        teamLocations.forEach { member ->
            val marker = AmapOfflineUtils.addTeamMemberMarker(osmMapView, member, requireContext())
            osmTeamMemberMarkers[member.userId] = marker
        }
        osmMapView.invalidate()
    }
    
    private fun showLocationTrack(locations: List<LocationData>) {
        if (isOnlineMode) {
            showAmapLocationTrack(locations)
        } else {
            showOsmLocationTrack(locations)
        }
    }
    
    private fun showAmapLocationTrack(locations: List<LocationData>) {
        val amap = this.amap ?: return
        
        amapTrackPolyline?.remove()
        
        if (locations.size < 2) return
        
        amapTrackPolyline = AmapOfflineUtils.addAmapTrackPolyline(amap, locations)
    }
    
    private fun showOsmLocationTrack(locations: List<LocationData>) {
        val osmMapView = this.osmMapView ?: return
        
        osmTrackPolyline?.let { osmMapView.overlays.remove(it) }
        
        if (locations.size < 2) return
        
        osmTrackPolyline = AmapOfflineUtils.addTrackPolyline(osmMapView, locations)
        osmMapView.invalidate()
    }
    
    private fun clearLocationTrack() {
        amapTrackPolyline?.remove()
        amapTrackPolyline = null
        
        osmTrackPolyline?.let { osmMapView?.overlays?.remove(it) }
        osmTrackPolyline = null
        osmMapView?.invalidate()
    }
    
    private fun downloadOfflineMap() {
        // 下载当前城市的离线地图（以北京为例）
        AmapOfflineUtils.downloadOfflineMapTiles(requireContext(), "110100") { success, message ->
            requireActivity().runOnUiThread {
                Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun updateSyncModeUI(mode: LocationSyncMode) {
        binding.btnSyncNow.visibility = if (mode == LocationSyncMode.MANUAL) View.VISIBLE else View.GONE
        binding.tvSyncMode.text = when (mode) {
            LocationSyncMode.AUTO -> "自动同步模式"
            LocationSyncMode.MANUAL -> "手动同步模式"
        }
    }
    
    override fun onResume() {
        super.onResume()
        amapView?.onResume()
        osmMapView?.onResume()
    }
    
    override fun onPause() {
        super.onPause()
        amapView?.onPause()
        osmMapView?.onPause()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        amapView?.onDestroy()
        osmMapView?.onDetach()
        _binding = null
    }
    
    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        amapView?.onSaveInstanceState(outState)
    }
}
