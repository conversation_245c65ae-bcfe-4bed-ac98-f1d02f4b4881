package com.rescue.app.service

import android.app.*
import android.content.Context
import android.content.Intent
import android.location.Location
import android.os.IBinder
import android.os.Looper
import androidx.core.app.NotificationCompat
import com.google.android.gms.location.*
import com.rescue.app.R
import com.rescue.app.RescueApplication
import com.rescue.app.domain.model.LocationSyncMode
import com.rescue.app.ui.main.MainActivity
import com.rescue.app.utils.LocationUtils
import kotlinx.coroutines.*

class LocationSyncService : Service() {
    
    companion object {
        const val ACTION_START_LOCATION_SYNC = "start_location_sync"
        const val ACTION_STOP_LOCATION_SYNC = "stop_location_sync"
        const val ACTION_SYNC_NOW = "sync_now"
        
        private const val NOTIFICATION_ID = 1001
        private const val LOCATION_UPDATE_INTERVAL = 30000L // 30秒
        private const val FASTEST_LOCATION_INTERVAL = 15000L // 15秒
        
        fun startService(context: Context) {
            val intent = Intent(context, LocationSyncService::class.java).apply {
                action = ACTION_START_LOCATION_SYNC
            }
            context.startForegroundService(intent)
        }
        
        fun stopService(context: Context) {
            val intent = Intent(context, LocationSyncService::class.java).apply {
                action = ACTION_STOP_LOCATION_SYNC
            }
            context.startService(intent)
        }
        
        fun syncNow(context: Context) {
            val intent = Intent(context, LocationSyncService::class.java).apply {
                action = ACTION_SYNC_NOW
            }
            context.startService(intent)
        }
    }
    
    private lateinit var fusedLocationClient: FusedLocationProviderClient
    private lateinit var locationRequest: LocationRequest
    private lateinit var locationCallback: LocationCallback
    
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var syncJob: Job? = null
    
    private val app get() = application as RescueApplication
    private val locationRepository get() = app.locationRepository
    private val userRepository get() = app.userRepository
    
    override fun onCreate() {
        super.onCreate()
        
        fusedLocationClient = LocationServices.getFusedLocationProviderClient(this)
        locationRequest = LocationUtils.createLocationRequest(LOCATION_UPDATE_INTERVAL)
        
        locationCallback = object : LocationCallback() {
            override fun onLocationResult(locationResult: LocationResult) {
                locationResult.lastLocation?.let { location ->
                    handleLocationUpdate(location)
                }
            }
        }
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_LOCATION_SYNC -> startLocationSync()
            ACTION_STOP_LOCATION_SYNC -> stopLocationSync()
            ACTION_SYNC_NOW -> syncLocationNow()
        }
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    private fun startLocationSync() {
        if (!LocationUtils.hasLocationPermission(this)) {
            stopSelf()
            return
        }
        
        val user = userRepository.getCurrentUser()
        if (user == null) {
            stopSelf()
            return
        }
        
        startForeground(NOTIFICATION_ID, createNotification())
        
        try {
            fusedLocationClient.requestLocationUpdates(
                locationRequest,
                locationCallback,
                Looper.getMainLooper()
            )
            
            startAutoSync()
        } catch (e: SecurityException) {
            stopSelf()
        }
    }
    
    private fun stopLocationSync() {
        fusedLocationClient.removeLocationUpdates(locationCallback)
        syncJob?.cancel()
        stopForeground(true)
        stopSelf()
    }
    
    private fun syncLocationNow() {
        serviceScope.launch {
            try {
                locationRepository.syncLocations().collect { resource ->
                    // 处理同步结果，可以发送广播通知UI更新
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun handleLocationUpdate(location: Location) {
        val user = userRepository.getCurrentUser() ?: return
        
        if (!LocationUtils.isAccurateEnough(location.accuracy)) {
            return // 精度不够，忽略此次位置更新
        }
        
        serviceScope.launch {
            try {
                val locationData = LocationUtils.locationToLocationData(location, user.id)
                locationRepository.insertLocation(locationData)
                
                // 如果是自动同步模式，检查是否需要立即同步
                if (locationRepository.getLocationSyncMode() == LocationSyncMode.AUTO) {
                    val lastSyncTime = app.preferenceManager.getLastLocationSyncTime()
                    val syncInterval = locationRepository.getLocationSyncInterval() * 60 * 1000 // 转换为毫秒
                    
                    if (System.currentTimeMillis() - lastSyncTime >= syncInterval) {
                        syncLocationNow()
                        app.preferenceManager.setLastLocationSyncTime(System.currentTimeMillis())
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun startAutoSync() {
        syncJob?.cancel()
        
        if (locationRepository.getLocationSyncMode() == LocationSyncMode.AUTO) {
            val syncInterval = locationRepository.getLocationSyncInterval() * 60 * 1000 // 转换为毫秒
            
            syncJob = serviceScope.launch {
                while (isActive) {
                    delay(syncInterval)
                    try {
                        locationRepository.syncLocations().collect { /* 处理结果 */ }
                        app.preferenceManager.setLastLocationSyncTime(System.currentTimeMillis())
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
        }
    }
    
    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, RescueApplication.LOCATION_NOTIFICATION_CHANNEL_ID)
            .setContentTitle("位置同步服务")
            .setContentText("正在同步GPS位置信息")
            .setSmallIcon(R.drawable.ic_location)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        fusedLocationClient.removeLocationUpdates(locationCallback)
        serviceScope.cancel()
    }
}
