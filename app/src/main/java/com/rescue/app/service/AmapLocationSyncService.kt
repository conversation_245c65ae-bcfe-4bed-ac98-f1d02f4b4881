package com.rescue.app.service

import android.app.*
import android.content.Context
import android.content.Intent
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.amap.api.location.AMapLocation
import com.amap.api.location.AMapLocationClient
import com.amap.api.location.AMapLocationListener
import com.rescue.app.R
import com.rescue.app.RescueApplication
import com.rescue.app.domain.model.LocationSyncMode
import com.rescue.app.ui.main.MainActivity
import com.rescue.app.utils.AmapOfflineUtils
import kotlinx.coroutines.*

class AmapLocationSyncService : Service(), AMapLocationListener {
    
    companion object {
        const val ACTION_START_LOCATION_SYNC = "start_location_sync"
        const val ACTION_STOP_LOCATION_SYNC = "stop_location_sync"
        const val ACTION_SYNC_NOW = "sync_now"
        
        private const val NOTIFICATION_ID = 1001
        
        fun startService(context: Context) {
            val intent = Intent(context, AmapLocationSyncService::class.java).apply {
                action = ACTION_START_LOCATION_SYNC
            }
            context.startForegroundService(intent)
        }
        
        fun stopService(context: Context) {
            val intent = Intent(context, AmapLocationSyncService::class.java).apply {
                action = ACTION_STOP_LOCATION_SYNC
            }
            context.startService(intent)
        }
        
        fun syncNow(context: Context) {
            val intent = Intent(context, AmapLocationSyncService::class.java).apply {
                action = ACTION_SYNC_NOW
            }
            context.startService(intent)
        }
    }
    
    private var locationClient: AMapLocationClient? = null
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var syncJob: Job? = null
    
    private val app get() = application as RescueApplication
    private val locationRepository get() = app.locationRepository
    private val userRepository get() = app.userRepository
    
    override fun onCreate() {
        super.onCreate()
        
        locationClient = AMapLocationClient(applicationContext)
        locationClient?.setLocationOption(AmapOfflineUtils.createLocationClientOption())
        locationClient?.setLocationListener(this)
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_LOCATION_SYNC -> startLocationSync()
            ACTION_STOP_LOCATION_SYNC -> stopLocationSync()
            ACTION_SYNC_NOW -> syncLocationNow()
        }
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    private fun startLocationSync() {
        val user = userRepository.getCurrentUser()
        if (user == null) {
            stopSelf()
            return
        }
        
        startForeground(NOTIFICATION_ID, createNotification())
        
        // 启动高德定位
        locationClient?.startLocation()
        
        startAutoSync()
    }
    
    private fun stopLocationSync() {
        locationClient?.stopLocation()
        syncJob?.cancel()
        stopForeground(true)
        stopSelf()
    }
    
    private fun syncLocationNow() {
        serviceScope.launch {
            try {
                locationRepository.syncLocations().collect { resource ->
                    // 处理同步结果，可以发送广播通知UI更新
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    override fun onLocationChanged(location: AMapLocation?) {
        location?.let { handleLocationUpdate(it) }
    }
    
    private fun handleLocationUpdate(location: AMapLocation) {
        val user = userRepository.getCurrentUser() ?: return
        
        serviceScope.launch {
            try {
                val locationData = AmapOfflineUtils.amapLocationToLocationData(location, user.id)
                if (locationData != null) {
                    locationRepository.insertLocation(locationData)
                    
                    // 如果是自动同步模式，检查是否需要立即同步
                    if (locationRepository.getLocationSyncMode() == LocationSyncMode.AUTO) {
                        val lastSyncTime = app.preferenceManager.getLastLocationSyncTime()
                        val syncInterval = locationRepository.getLocationSyncInterval() * 60 * 1000 // 转换为毫秒
                        
                        if (System.currentTimeMillis() - lastSyncTime >= syncInterval) {
                            syncLocationNow()
                            app.preferenceManager.setLastLocationSyncTime(System.currentTimeMillis())
                        }
                    }
                } else {
                    val errorMsg = AmapOfflineUtils.getLocationErrorMessage(location.errorCode)
                    android.util.Log.e("LocationSync", "定位失败: $errorMsg")
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun startAutoSync() {
        syncJob?.cancel()
        
        if (locationRepository.getLocationSyncMode() == LocationSyncMode.AUTO) {
            val syncInterval = locationRepository.getLocationSyncInterval() * 60 * 1000 // 转换为毫秒
            
            syncJob = serviceScope.launch {
                while (isActive) {
                    delay(syncInterval)
                    try {
                        locationRepository.syncLocations().collect { /* 处理结果 */ }
                        app.preferenceManager.setLastLocationSyncTime(System.currentTimeMillis())
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
        }
    }
    
    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, RescueApplication.LOCATION_NOTIFICATION_CHANNEL_ID)
            .setContentTitle("位置同步服务")
            .setContentText("正在使用高德地图同步GPS位置信息")
            .setSmallIcon(R.drawable.ic_location)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        locationClient?.stopLocation()
        locationClient?.onDestroy()
        serviceScope.cancel()
    }
}
