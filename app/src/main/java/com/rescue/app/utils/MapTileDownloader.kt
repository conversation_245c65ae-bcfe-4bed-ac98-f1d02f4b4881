package com.rescue.app.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.*
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import kotlin.math.*

class MapTileDownloader(private val context: Context) {
    
    companion object {
        private const val TAG = "MapTileDownloader"
        
        // 高德地图瓦片URL模板
        private const val AMAP_TILE_URL = "https://webrd01.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}"
        
        // OpenStreetMap瓦片URL模板
        private const val OSM_TILE_URL = "https://tile.openstreetmap.org/{z}/{x}/{y}.png"
        
        // 天地图瓦片URL模板
        private const val TIANDITU_TILE_URL = "http://t0.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=YOUR_TIANDITU_KEY"
    }
    
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
        .readTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
        .build()
    
    /**
     * 下载指定区域的地图瓦片
     */
    suspend fun downloadTilesForArea(
        centerLat: Double,
        centerLon: Double,
        radiusKm: Double,
        minZoom: Int = 10,
        maxZoom: Int = 16,
        tileSource: TileSource = TileSource.AMAP,
        onProgress: (current: Int, total: Int) -> Unit = { _, _ -> },
        onComplete: (success: Boolean, message: String) -> Unit
    ) = withContext(Dispatchers.IO) {
        
        try {
            val tilesDir = File(context.getExternalFilesDir(null), "tiles")
            if (!tilesDir.exists()) {
                tilesDir.mkdirs()
            }
            
            val tilesToDownload = mutableListOf<TileInfo>()
            
            // 计算需要下载的瓦片范围
            for (zoom in minZoom..maxZoom) {
                val bounds = calculateTileBounds(centerLat, centerLon, radiusKm, zoom)
                
                for (x in bounds.minX..bounds.maxX) {
                    for (y in bounds.minY..bounds.maxY) {
                        tilesToDownload.add(TileInfo(x, y, zoom))
                    }
                }
            }
            
            Log.d(TAG, "需要下载 ${tilesToDownload.size} 个瓦片")
            
            var downloadedCount = 0
            var failedCount = 0
            
            // 并发下载瓦片
            val semaphore = kotlinx.coroutines.sync.Semaphore(5) // 限制并发数
            
            tilesToDownload.map { tileInfo ->
                async {
                    semaphore.withPermit {
                        val success = downloadTile(tileInfo, tileSource, tilesDir)
                        if (success) {
                            downloadedCount++
                        } else {
                            failedCount++
                        }
                        
                        withContext(Dispatchers.Main) {
                            onProgress(downloadedCount + failedCount, tilesToDownload.size)
                        }
                    }
                }
            }.awaitAll()
            
            withContext(Dispatchers.Main) {
                val message = "下载完成: 成功 $downloadedCount 个, 失败 $failedCount 个"
                onComplete(failedCount == 0, message)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "下载瓦片失败", e)
            withContext(Dispatchers.Main) {
                onComplete(false, "下载失败: ${e.message}")
            }
        }
    }
    
    /**
     * 下载单个瓦片
     */
    private suspend fun downloadTile(tileInfo: TileInfo, tileSource: TileSource, tilesDir: File): Boolean {
        return try {
            val tileFile = File(tilesDir, "${tileInfo.z}/${tileInfo.x}/${tileInfo.y}.png")
            
            // 如果文件已存在，跳过下载
            if (tileFile.exists()) {
                return true
            }
            
            // 创建目录
            tileFile.parentFile?.mkdirs()
            
            val url = getTileUrl(tileInfo, tileSource)
            val request = Request.Builder()
                .url(url)
                .addHeader("User-Agent", "Mozilla/5.0 (Android)")
                .build()
            
            val response = httpClient.newCall(request).execute()
            
            if (response.isSuccessful) {
                response.body?.byteStream()?.use { inputStream ->
                    FileOutputStream(tileFile).use { outputStream ->
                        inputStream.copyTo(outputStream)
                    }
                }
                true
            } else {
                Log.w(TAG, "下载瓦片失败: ${response.code} - $url")
                false
            }
        } catch (e: IOException) {
            Log.e(TAG, "下载瓦片异常: ${tileInfo.z}/${tileInfo.x}/${tileInfo.y}", e)
            false
        }
    }
    
    /**
     * 获取瓦片URL
     */
    private fun getTileUrl(tileInfo: TileInfo, tileSource: TileSource): String {
        return when (tileSource) {
            TileSource.AMAP -> AMAP_TILE_URL
                .replace("{x}", tileInfo.x.toString())
                .replace("{y}", tileInfo.y.toString())
                .replace("{z}", tileInfo.z.toString())
            
            TileSource.OSM -> OSM_TILE_URL
                .replace("{x}", tileInfo.x.toString())
                .replace("{y}", tileInfo.y.toString())
                .replace("{z}", tileInfo.z.toString())
            
            TileSource.TIANDITU -> TIANDITU_TILE_URL
                .replace("{x}", tileInfo.x.toString())
                .replace("{y}", tileInfo.y.toString())
                .replace("{z}", tileInfo.z.toString())
        }
    }
    
    /**
     * 计算指定区域的瓦片边界
     */
    private fun calculateTileBounds(centerLat: Double, centerLon: Double, radiusKm: Double, zoom: Int): TileBounds {
        // 将半径转换为经纬度偏移量
        val latOffset = radiusKm / 111.0 // 1度纬度约等于111公里
        val lonOffset = radiusKm / (111.0 * cos(Math.toRadians(centerLat)))
        
        val minLat = centerLat - latOffset
        val maxLat = centerLat + latOffset
        val minLon = centerLon - lonOffset
        val maxLon = centerLon + lonOffset
        
        // 转换为瓦片坐标
        val minTileX = lon2tile(minLon, zoom)
        val maxTileX = lon2tile(maxLon, zoom)
        val minTileY = lat2tile(maxLat, zoom) // 注意：纬度和瓦片Y坐标是反向的
        val maxTileY = lat2tile(minLat, zoom)
        
        return TileBounds(minTileX, maxTileX, minTileY, maxTileY)
    }
    
    /**
     * 经度转瓦片X坐标
     */
    private fun lon2tile(lon: Double, zoom: Int): Int {
        return floor((lon + 180.0) / 360.0 * (1 shl zoom)).toInt()
    }
    
    /**
     * 纬度转瓦片Y坐标
     */
    private fun lat2tile(lat: Double, zoom: Int): Int {
        return floor((1.0 - ln(tan(Math.toRadians(lat)) + 1.0 / cos(Math.toRadians(lat))) / PI) / 2.0 * (1 shl zoom)).toInt()
    }
    
    /**
     * 获取已下载的瓦片数量
     */
    fun getDownloadedTileCount(): Int {
        val tilesDir = File(context.getExternalFilesDir(null), "tiles")
        return if (tilesDir.exists()) {
            tilesDir.walkTopDown().count { it.isFile && it.extension == "png" }
        } else {
            0
        }
    }
    
    /**
     * 清除所有下载的瓦片
     */
    fun clearDownloadedTiles(): Boolean {
        val tilesDir = File(context.getExternalFilesDir(null), "tiles")
        return if (tilesDir.exists()) {
            tilesDir.deleteRecursively()
        } else {
            true
        }
    }
    
    /**
     * 瓦片信息
     */
    data class TileInfo(val x: Int, val y: Int, val z: Int)
    
    /**
     * 瓦片边界
     */
    data class TileBounds(val minX: Int, val maxX: Int, val minY: Int, val maxY: Int)
    
    /**
     * 瓦片源类型
     */
    enum class TileSource {
        AMAP,      // 高德地图
        OSM,       // OpenStreetMap
        TIANDITU   // 天地图
    }
}
