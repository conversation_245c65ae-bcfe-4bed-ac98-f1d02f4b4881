package com.rescue.app.utils

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.rescue.app.domain.model.LocationSyncMode
import com.rescue.app.domain.model.User

class PreferenceManager(context: Context) {
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()
    
    companion object {
        private const val PREF_NAME = "rescue_app_prefs"
        private const val KEY_AUTH_TOKEN = "auth_token"
        private const val KEY_USER = "user"
        private const val KEY_IS_LOGGED_IN = "is_logged_in"
        private const val KEY_LOCATION_SYNC_MODE = "location_sync_mode"
        private const val KEY_LOCATION_SYNC_INTERVAL = "location_sync_interval"
        private const val KEY_FIRST_LAUNCH = "first_launch"
        private const val KEY_LAST_LOCATION_SYNC = "last_location_sync"
        
        private const val DEFAULT_SYNC_INTERVAL_MINUTES = 5L
    }
    
    // 认证相关
    fun saveAuthToken(token: String) {
        sharedPreferences.edit().putString(KEY_AUTH_TOKEN, token).apply()
    }
    
    fun getAuthToken(): String? {
        return sharedPreferences.getString(KEY_AUTH_TOKEN, null)
    }
    
    fun clearAuthToken() {
        sharedPreferences.edit().remove(KEY_AUTH_TOKEN).apply()
    }
    
    // 用户信息相关
    fun saveUser(user: User) {
        val userJson = gson.toJson(user)
        sharedPreferences.edit().putString(KEY_USER, userJson).apply()
    }
    
    fun getUser(): User? {
        val userJson = sharedPreferences.getString(KEY_USER, null)
        return if (userJson != null) {
            try {
                gson.fromJson(userJson, User::class.java)
            } catch (e: Exception) {
                null
            }
        } else {
            null
        }
    }
    
    fun clearUser() {
        sharedPreferences.edit().remove(KEY_USER).apply()
    }
    
    // 登录状态
    fun setLoggedIn(isLoggedIn: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_IS_LOGGED_IN, isLoggedIn).apply()
    }
    
    fun isLoggedIn(): Boolean {
        return sharedPreferences.getBoolean(KEY_IS_LOGGED_IN, false)
    }
    
    // 位置同步设置
    fun setLocationSyncMode(mode: LocationSyncMode) {
        sharedPreferences.edit().putString(KEY_LOCATION_SYNC_MODE, mode.name).apply()
    }
    
    fun getLocationSyncMode(): LocationSyncMode {
        val modeString = sharedPreferences.getString(KEY_LOCATION_SYNC_MODE, LocationSyncMode.AUTO.name)
        return try {
            LocationSyncMode.valueOf(modeString!!)
        } catch (e: Exception) {
            LocationSyncMode.AUTO
        }
    }
    
    fun setLocationSyncInterval(intervalMinutes: Long) {
        sharedPreferences.edit().putLong(KEY_LOCATION_SYNC_INTERVAL, intervalMinutes).apply()
    }
    
    fun getLocationSyncInterval(): Long {
        return sharedPreferences.getLong(KEY_LOCATION_SYNC_INTERVAL, DEFAULT_SYNC_INTERVAL_MINUTES)
    }
    
    // 首次启动
    fun isFirstLaunch(): Boolean {
        return sharedPreferences.getBoolean(KEY_FIRST_LAUNCH, true)
    }
    
    fun setFirstLaunch(isFirst: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_FIRST_LAUNCH, isFirst).apply()
    }
    
    // 最后同步时间
    fun setLastLocationSyncTime(timestamp: Long) {
        sharedPreferences.edit().putLong(KEY_LAST_LOCATION_SYNC, timestamp).apply()
    }
    
    fun getLastLocationSyncTime(): Long {
        return sharedPreferences.getLong(KEY_LAST_LOCATION_SYNC, 0)
    }
    
    // 清除所有数据
    fun clearAll() {
        sharedPreferences.edit().clear().apply()
    }
}
