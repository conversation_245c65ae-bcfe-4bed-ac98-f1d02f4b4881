package com.rescue.app.utils

import android.content.Context
import android.graphics.BitmapFactory
import com.amap.api.location.AMapLocationClient
import com.amap.api.location.AMapLocationClientOption
import com.amap.api.location.AMapLocation
import com.amap.api.maps.AMap
import com.amap.api.maps.CameraUpdateFactory
import com.amap.api.maps.model.*
import com.amap.api.maps.offlinemap.OfflineMapManager
import com.rescue.app.R
import com.rescue.app.domain.model.LocationData
import com.rescue.app.domain.model.TeamMemberLocation
import org.osmdroid.config.Configuration
import org.osmdroid.tileprovider.MapTileProviderBasic
import org.osmdroid.tileprovider.tilesource.ITileSource
import org.osmdroid.tileprovider.tilesource.XYTileSource
import org.osmdroid.util.GeoPoint
import org.osmdroid.views.MapView
import org.osmdroid.views.overlay.Marker
import org.osmdroid.views.overlay.Polyline
import java.io.File

object AmapOfflineUtils {
    
    /**
     * 初始化高德地图
     */
    fun initAmap(context: Context) {
        // 初始化定位客户端
        AMapLocationClient.updatePrivacyShow(context, true, true)
        AMapLocationClient.updatePrivacyAgree(context, true)
        
        // 初始化OSMDroid配置
        Configuration.getInstance().load(context, context.getSharedPreferences("osmdroid", Context.MODE_PRIVATE))
        Configuration.getInstance().userAgentValue = context.packageName
    }
    
    /**
     * 创建高德定位配置
     */
    fun createLocationClientOption(): AMapLocationClientOption {
        val option = AMapLocationClientOption()
        
        // 设置定位模式为高精度模式
        option.locationMode = AMapLocationClientOption.AMapLocationMode.Hight_Accuracy
        
        // 设置定位间隔,单位毫秒,默认为2000ms
        option.interval = 30000
        
        // 设置是否返回地址信息（默认返回地址信息）
        option.isNeedAddress = true
        
        // 设置是否允许模拟位置,默认为false，不允许模拟位置
        option.isMockEnable = false
        
        // 关闭缓存机制
        option.isLocationCacheEnable = false
        
        // 设置是否单次定位
        option.isOnceLocation = false
        
        // 设置是否开启wifi扫描
        option.isWifiScan = true
        
        // 设置定位协议
        option.locationProtocol = AMapLocationClientOption.AMapLocationProtocol.HTTP
        
        return option
    }
    
    /**
     * 将高德定位结果转换为LocationData
     */
    fun amapLocationToLocationData(location: AMapLocation, userId: String): LocationData? {
        if (location.errorCode == 0) {
            return LocationData(
                userId = userId,
                latitude = location.latitude,
                longitude = location.longitude,
                altitude = location.altitude,
                accuracy = location.accuracy,
                speed = location.speed,
                bearing = location.bearing,
                timestamp = location.time
            )
        }
        return null
    }
    
    /**
     * 创建离线地图瓦片源
     */
    fun createOfflineTileSource(context: Context): ITileSource {
        val tilesDir = File(context.getExternalFilesDir(null), "tiles")
        if (!tilesDir.exists()) {
            tilesDir.mkdirs()
        }
        
        return object : XYTileSource(
            "OfflineMap",
            1, 18, 256, ".png",
            arrayOf("file://${tilesDir.absolutePath}/")
        ) {
            override fun getTileURLString(pMapTileIndex: Long): String {
                val x = getX(pMapTileIndex)
                val y = getY(pMapTileIndex)
                val z = getZ(pMapTileIndex)
                return "file://${tilesDir.absolutePath}/$z/$x/$y.png"
            }
        }
    }
    
    /**
     * 配置OSMDroid地图视图
     */
    fun setupOSMMapView(mapView: MapView, context: Context) {
        mapView.apply {
            setTileSource(createOfflineTileSource(context))
            setMultiTouchControls(true)
            setBuiltInZoomControls(true)
            controller.setZoom(15.0)
            
            // 设置地图中心点（默认北京）
            controller.setCenter(GeoPoint(39.9042, 116.4074))
        }
    }
    
    /**
     * 在OSMDroid地图上添加当前位置标记
     */
    fun addCurrentLocationMarker(mapView: MapView, location: LocationData, context: Context): Marker {
        val marker = Marker(mapView)
        marker.position = GeoPoint(location.latitude, location.longitude)
        marker.title = "我的位置"
        marker.snippet = "精度: ${location.accuracy}m"
        
        // 设置自定义图标
        val icon = BitmapFactory.decodeResource(context.resources, R.drawable.ic_my_location)
        marker.icon = context.resources.getDrawable(R.drawable.ic_my_location, null)
        
        mapView.overlays.add(marker)
        return marker
    }
    
    /**
     * 在OSMDroid地图上添加队友位置标记
     */
    fun addTeamMemberMarker(mapView: MapView, member: TeamMemberLocation, context: Context): Marker {
        val marker = Marker(mapView)
        marker.position = GeoPoint(member.latitude, member.longitude)
        marker.title = member.name
        marker.snippet = "${member.username} - ${if (member.isOnline) "在线" else "离线"}"
        
        // 根据在线状态设置不同颜色的图标
        val iconRes = if (member.isOnline) R.drawable.ic_marker_green else R.drawable.ic_marker_red
        marker.icon = context.resources.getDrawable(iconRes, null)
        
        mapView.overlays.add(marker)
        return marker
    }
    
    /**
     * 在OSMDroid地图上添加轨迹线
     */
    fun addTrackPolyline(mapView: MapView, locations: List<LocationData>): Polyline {
        val polyline = Polyline()
        val points = locations.map { GeoPoint(it.latitude, it.longitude) }
        polyline.setPoints(points)
        polyline.color = 0xFF2196F3.toInt()
        polyline.width = 8f
        
        mapView.overlays.add(polyline)
        return polyline
    }
    
    /**
     * 移动OSMDroid地图到指定位置
     */
    fun moveMapToLocation(mapView: MapView, latitude: Double, longitude: Double, zoom: Double = 15.0) {
        mapView.controller.animateTo(GeoPoint(latitude, longitude))
        mapView.controller.setZoom(zoom)
    }
    
    /**
     * 高德地图相关工具方法
     */
    
    /**
     * 在高德地图上添加当前位置标记
     */
    fun addAmapCurrentLocationMarker(amap: AMap, location: LocationData, context: Context): com.amap.api.maps.model.Marker {
        val markerOptions = MarkerOptions()
            .position(LatLng(location.latitude, location.longitude))
            .title("我的位置")
            .snippet("精度: ${location.accuracy}m")
            .icon(BitmapDescriptorFactory.fromResource(R.drawable.ic_my_location))
        
        return amap.addMarker(markerOptions)
    }
    
    /**
     * 在高德地图上添加队友位置标记
     */
    fun addAmapTeamMemberMarker(amap: AMap, member: TeamMemberLocation): com.amap.api.maps.model.Marker {
        val iconRes = if (member.isOnline) R.drawable.ic_marker_green else R.drawable.ic_marker_red
        
        val markerOptions = MarkerOptions()
            .position(LatLng(member.latitude, member.longitude))
            .title(member.name)
            .snippet("${member.username} - ${if (member.isOnline) "在线" else "离线"}")
            .icon(BitmapDescriptorFactory.fromResource(iconRes))
        
        return amap.addMarker(markerOptions)
    }
    
    /**
     * 在高德地图上添加轨迹线
     */
    fun addAmapTrackPolyline(amap: AMap, locations: List<LocationData>): com.amap.api.maps.model.Polyline {
        val points = locations.map { LatLng(it.latitude, it.longitude) }
        
        val polylineOptions = PolylineOptions()
            .addAll(points)
            .width(8f)
            .color(0xFF2196F3.toInt())
        
        return amap.addPolyline(polylineOptions)
    }
    
    /**
     * 移动高德地图到指定位置
     */
    fun moveAmapToLocation(amap: AMap, latitude: Double, longitude: Double, zoom: Float = 15f) {
        val cameraUpdate = CameraUpdateFactory.newLatLngZoom(LatLng(latitude, longitude), zoom)
        amap.animateCamera(cameraUpdate)
    }
    
    /**
     * 下载离线地图瓦片
     */
    fun downloadOfflineMapTiles(context: Context, cityCode: String, callback: (Boolean, String) -> Unit) {
        val offlineMapManager = OfflineMapManager(context, object : OfflineMapManager.OfflineMapDownloadListener {
            override fun onDownload(status: Int, completeCode: Int, downInfo: String?) {
                when (status) {
                    OfflineMapManager.SUCCESS -> {
                        callback(true, "下载成功")
                    }
                    OfflineMapManager.LOADING -> {
                        // 下载中
                    }
                    OfflineMapManager.UNZIP -> {
                        // 解压中
                    }
                    OfflineMapManager.EXCEPTION_AMAP -> {
                        callback(false, "高德地图异常")
                    }
                    OfflineMapManager.EXCEPTION_NETWORK_LOADING -> {
                        callback(false, "网络异常")
                    }
                    OfflineMapManager.EXCEPTION_SDCARD -> {
                        callback(false, "SD卡异常")
                    }
                }
            }
            
            override fun onCheckUpdate(hasNew: Boolean, name: String?) {
                // 检查更新回调
            }
            
            override fun onRemove(success: Boolean, name: String?, describe: String?) {
                // 删除回调
            }
        })
        
        // 开始下载指定城市的离线地图
        offlineMapManager.downloadByCityCode(cityCode)
    }
    
    /**
     * 获取定位错误信息
     */
    fun getLocationErrorMessage(errorCode: Int): String {
        return when (errorCode) {
            0 -> "定位成功"
            1 -> "一些重要参数为空"
            2 -> "网络异常"
            3 -> "获取到的请求参数为空"
            4 -> "网络连接异常或网络超时"
            5 -> "加密访问串有问题或者时间戳参数有问题"
            6 -> "无对应的用户Key"
            7 -> "Key格式错误"
            8 -> "Key不存在或者Key过期"
            9 -> "Key被删除"
            10 -> "Key被冻结"
            11 -> "访问IP不在白名单内"
            12 -> "定位失败"
            13 -> "定位缓存的结果"
            14 -> "启动整体定位超时"
            else -> "未知错误，错误码：$errorCode"
        }
    }
}
