package com.rescue.app.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.location.Location
import androidx.core.app.ActivityCompat
import com.google.android.gms.location.*
import com.rescue.app.domain.model.LocationData
import kotlin.math.*

object LocationUtils {
    
    /**
     * 检查位置权限
     */
    fun hasLocationPermission(context: Context): <PERSON><PERSON>an {
        return ActivityCompat.checkSelfPermission(
            context,
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查后台位置权限
     */
    fun hasBackgroundLocationPermission(context: Context): <PERSON><PERSON><PERSON> {
        return if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
            ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.ACCESS_BACKGROUND_LOCATION
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true // Android 10以下不需要后台位置权限
        }
    }
    
    /**
     * 创建位置请求配置
     */
    fun createLocationRequest(intervalMillis: Long = 30000): LocationRequest {
        return LocationRequest.Builder(Priority.PRIORITY_HIGH_ACCURACY, intervalMillis)
            .setWaitForAccurateLocation(false)
            .setMinUpdateIntervalMillis(intervalMillis / 2)
            .setMaxUpdateDelayMillis(intervalMillis * 2)
            .build()
    }
    
    /**
     * 将Android Location转换为LocationData
     */
    fun locationToLocationData(location: Location, userId: String): LocationData {
        return LocationData(
            userId = userId,
            latitude = location.latitude,
            longitude = location.longitude,
            altitude = location.altitude,
            accuracy = location.accuracy,
            speed = location.speed,
            bearing = location.bearing,
            timestamp = location.time
        )
    }
    
    /**
     * 计算两点之间的距离（米）
     */
    fun calculateDistance(lat1: Double, lon1: Double, lat2: Double, lon2: Double): Double {
        val earthRadius = 6371000.0 // 地球半径（米）
        
        val dLat = Math.toRadians(lat2 - lat1)
        val dLon = Math.toRadians(lon2 - lon1)
        
        val a = sin(dLat / 2) * sin(dLat / 2) +
                cos(Math.toRadians(lat1)) * cos(Math.toRadians(lat2)) *
                sin(dLon / 2) * sin(dLon / 2)
        
        val c = 2 * atan2(sqrt(a), sqrt(1 - a))
        
        return earthRadius * c
    }
    
    /**
     * 计算两个LocationData之间的距离
     */
    fun calculateDistance(location1: LocationData, location2: LocationData): Double {
        return calculateDistance(
            location1.latitude, location1.longitude,
            location2.latitude, location2.longitude
        )
    }
    
    /**
     * 格式化坐标显示
     */
    fun formatCoordinate(latitude: Double, longitude: Double): String {
        return String.format("%.6f, %.6f", latitude, longitude)
    }
    
    /**
     * 格式化距离显示
     */
    fun formatDistance(distanceInMeters: Double): String {
        return when {
            distanceInMeters < 1000 -> String.format("%.0f米", distanceInMeters)
            distanceInMeters < 10000 -> String.format("%.1f公里", distanceInMeters / 1000)
            else -> String.format("%.0f公里", distanceInMeters / 1000)
        }
    }
    
    /**
     * 检查位置是否有效
     */
    fun isValidLocation(latitude: Double, longitude: Double): Boolean {
        return latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180
    }
    
    /**
     * 检查位置精度是否足够
     */
    fun isAccurateEnough(accuracy: Float, threshold: Float = 50f): Boolean {
        return accuracy <= threshold
    }
    
    /**
     * 计算速度（公里/小时）
     */
    fun calculateSpeed(distance: Double, timeMillis: Long): Double {
        if (timeMillis <= 0) return 0.0
        val timeHours = timeMillis / (1000.0 * 60.0 * 60.0)
        val distanceKm = distance / 1000.0
        return distanceKm / timeHours
    }
}
