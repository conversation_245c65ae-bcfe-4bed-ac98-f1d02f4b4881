package com.rescue.app.utils

import android.content.Context
import com.baidu.location.BDAbstractLocationListener
import com.baidu.location.BDLocation
import com.baidu.location.LocationClient
import com.baidu.location.LocationClientOption
import com.baidu.mapapi.CoordType
import com.baidu.mapapi.SDKInitializer
import com.baidu.mapapi.map.*
import com.baidu.mapapi.model.LatLng
import com.baidu.mapapi.utils.DistanceUtil
import com.rescue.app.domain.model.LocationData
import com.rescue.app.domain.model.TeamMemberLocation

object BaiduMapUtils {
    
    /**
     * 初始化百度地图SDK
     */
    fun initBaiduMap(context: Context) {
        // 在使用SDK各组件之前初始化context信息，传入ApplicationContext
        SDKInitializer.initialize(context.applicationContext)
        // 自4.3.0起，百度地图SDK所有接口均支持百度坐标和国测局坐标，用此方法设置您使用的坐标类型.
        // 包括BD09LL和GCJ02两种坐标，默认是BD09LL坐标。
        SDKInitializer.setCoordType(CoordType.BD09LL)
    }
    
    /**
     * 创建位置客户端配置
     */
    fun createLocationClientOption(): LocationClientOption {
        val option = LocationClientOption()
        
        // 设置定位模式，三选一
        option.locationMode = LocationClientOption.LocationMode.Hight_Accuracy
        
        // 设置是否使用gps
        option.isOpenGps = true
        
        // 设置扫描间隔，单位是毫秒 当<1000(1s)时，定时定位无效
        option.setScanSpan(30000) // 30秒
        
        // 设置 true 表示启动室内定位
        option.isLocationNotify = true
        
        // 设置是否当gps有效时按照1S1次频率输出GPS结果
        option.isIgnoreKillProcess = false
        
        // 设置是否需要地址信息，默认不需要
        option.setIsNeedAddress(true)
        
        // 设置是否需要位置语义化结果
        option.setIsNeedLocationDescribe(true)
        
        // 设置是否需要POI结果
        option.setIsNeedLocationPoiList(true)
        
        // 设置是否收集CRASH信息，默认收集
        option.SetIgnoreCacheException(false)
        
        // 设置坐标类型
        option.setCoorType("bd09ll")
        
        return option
    }
    
    /**
     * 将百度定位结果转换为LocationData
     */
    fun bdLocationToLocationData(location: BDLocation, userId: String): LocationData? {
        if (location.locType == BDLocation.TypeGpsLocation ||
            location.locType == BDLocation.TypeNetWorkLocation ||
            location.locType == BDLocation.TypeOffLineLocation) {
            
            return LocationData(
                userId = userId,
                latitude = location.latitude,
                longitude = location.longitude,
                altitude = location.altitude,
                accuracy = location.radius,
                speed = location.speed,
                bearing = location.direction,
                timestamp = location.time
            )
        }
        return null
    }
    
    /**
     * 创建地图标记选项
     */
    fun createMarkerOptions(
        position: LatLng,
        title: String,
        iconResourceId: Int? = null
    ): MarkerOptions {
        val markerOptions = MarkerOptions()
            .position(position)
            .title(title)
            .draggable(false)
        
        iconResourceId?.let {
            val bitmap = BitmapDescriptorFactory.fromResource(it)
            markerOptions.icon(bitmap)
        }
        
        return markerOptions
    }
    
    /**
     * 创建当前位置标记
     */
    fun createCurrentLocationMarker(location: LocationData): MarkerOptions {
        val position = LatLng(location.latitude, location.longitude)
        return MarkerOptions()
            .position(position)
            .title("我的位置")
            .icon(BitmapDescriptorFactory.fromResource(com.baidu.mapapi.R.drawable.icon_marka))
    }
    
    /**
     * 创建队友位置标记
     */
    fun createTeamMemberMarker(member: TeamMemberLocation): MarkerOptions {
        val position = LatLng(member.latitude, member.longitude)
        val iconRes = if (member.isOnline) {
            com.baidu.mapapi.R.drawable.icon_markb // 在线 - 绿色
        } else {
            com.baidu.mapapi.R.drawable.icon_markc // 离线 - 红色
        }
        
        return MarkerOptions()
            .position(position)
            .title(member.name)
            .icon(BitmapDescriptorFactory.fromResource(iconRes))
    }
    
    /**
     * 创建轨迹线
     */
    fun createPolylineOptions(locations: List<LocationData>): PolylineOptions {
        val points = locations.map { LatLng(it.latitude, it.longitude) }
        
        return PolylineOptions()
            .points(points)
            .width(8)
            .color(0xFF2196F3.toInt()) // 蓝色
            .dottedLine(true)
    }
    
    /**
     * 计算两点之间的距离
     */
    fun calculateDistance(lat1: Double, lon1: Double, lat2: Double, lon2: Double): Double {
        val start = LatLng(lat1, lon1)
        val end = LatLng(lat2, lon2)
        return DistanceUtil.getDistance(start, end)
    }
    
    /**
     * 移动地图到指定位置
     */
    fun moveMapToLocation(baiduMap: BaiduMap, latitude: Double, longitude: Double, zoom: Float = 15f) {
        val latLng = LatLng(latitude, longitude)
        val mapStatus = MapStatus.Builder()
            .target(latLng)
            .zoom(zoom)
            .build()
        val mapStatusUpdate = MapStatusUpdateFactory.newMapStatus(mapStatus)
        baiduMap.animateMapStatus(mapStatusUpdate)
    }
    
    /**
     * 设置地图显示范围以包含所有标记点
     */
    fun fitMapToMarkers(baiduMap: BaiduMap, locations: List<LatLng>) {
        if (locations.isEmpty()) return
        
        if (locations.size == 1) {
            moveMapToLocation(baiduMap, locations[0].latitude, locations[0].longitude)
            return
        }
        
        val builder = LatLngBounds.Builder()
        locations.forEach { builder.include(it) }
        val bounds = builder.build()
        
        val mapStatusUpdate = MapStatusUpdateFactory.newLatLngBounds(bounds)
        baiduMap.animateMapStatus(mapStatusUpdate)
    }
    
    /**
     * 格式化定位错误信息
     */
    fun getLocationErrorMessage(locType: Int): String {
        return when (locType) {
            BDLocation.TypeGpsLocation -> "GPS定位成功"
            BDLocation.TypeNetWorkLocation -> "网络定位成功"
            BDLocation.TypeOffLineLocation -> "离线定位成功"
            BDLocation.TypeServerError -> "服务端网络定位失败"
            BDLocation.TypeNetWorkException -> "网络不通导致定位失败"
            BDLocation.TypeCriteriaException -> "无法获取有效定位依据"
            else -> "定位失败，错误码：$locType"
        }
    }
}

/**
 * 百度定位监听器的简化版本
 */
abstract class SimpleBDLocationListener : BDAbstractLocationListener() {
    abstract fun onLocationSuccess(location: BDLocation)
    abstract fun onLocationFailed(location: BDLocation)
    
    override fun onReceiveLocation(location: BDLocation?) {
        location?.let {
            if (it.locType == BDLocation.TypeGpsLocation ||
                it.locType == BDLocation.TypeNetWorkLocation ||
                it.locType == BDLocation.TypeOffLineLocation) {
                onLocationSuccess(it)
            } else {
                onLocationFailed(it)
            }
        }
    }
}
