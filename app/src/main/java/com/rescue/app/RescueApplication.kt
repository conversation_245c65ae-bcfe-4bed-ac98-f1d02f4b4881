package com.rescue.app

import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import com.rescue.app.data.local.RescueDatabase
import com.rescue.app.data.remote.ApiService
import com.rescue.app.data.repository.LocationRepository
import com.rescue.app.data.repository.MissionRepository
import com.rescue.app.data.repository.UserRepository
import com.rescue.app.utils.PreferenceManager

class RescueApplication : Application() {
    
    companion object {
        const val LOCATION_NOTIFICATION_CHANNEL_ID = "location_service_channel"
        const val MISSION_NOTIFICATION_CHANNEL_ID = "mission_notification_channel"
        
        lateinit var instance: RescueApplication
            private set
    }
    
    // 数据库实例
    val database by lazy { RescueDatabase.getDatabase(this) }
    
    // API服务实例
    val apiService by lazy { ApiService.create() }
    
    // 偏好设置管理器
    val preferenceManager by lazy { PreferenceManager(this) }
    
    // Repository实例
    val userRepository by lazy { 
        UserRepository(apiService, preferenceManager) 
    }
    
    val locationRepository by lazy { 
        LocationRepository(database.locationDao(), apiService, preferenceManager) 
    }
    
    val missionRepository by lazy { 
        MissionRepository(database.missionDao(), apiService, preferenceManager) 
    }

    override fun onCreate() {
        super.onCreate()
        instance = this
        
        createNotificationChannels()
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            
            // 位置服务通知渠道
            val locationChannel = NotificationChannel(
                LOCATION_NOTIFICATION_CHANNEL_ID,
                "位置同步服务",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "用于显示GPS位置同步服务状态"
                setShowBadge(false)
            }
            
            // 任务通知渠道
            val missionChannel = NotificationChannel(
                MISSION_NOTIFICATION_CHANNEL_ID,
                "搜救任务通知",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "用于接收和显示搜救任务通知"
                enableVibration(true)
                setShowBadge(true)
            }
            
            notificationManager.createNotificationChannel(locationChannel)
            notificationManager.createNotificationChannel(missionChannel)
        }
    }
}
