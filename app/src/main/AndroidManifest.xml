<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    
    <!-- GPS定位权限 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    
    <!-- 前台服务权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
    
    <!-- 唤醒锁权限 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    
    <!-- 振动权限 -->
    <uses-permission android:name="android.permission.VIBRATE" />
    
    <!-- 通知权限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <application
        android:name=".RescueApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.RescueApp"
        tools:targetApi="31">
        
        <!-- 高德地图API Key -->
        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="YOUR_AMAP_API_KEY" />

        <!-- 高德地图服务 -->
        <service
            android:name="com.amap.api.location.APSService"
            android:enabled="true"
            android:process=":remote" />

        <!-- 文件访问权限 -->
        <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
        <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
        
        <!-- 主Activity -->
        <activity
            android:name=".ui.main.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.RescueApp">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <!-- 登录Activity -->
        <activity
            android:name=".ui.login.LoginActivity"
            android:exported="false"
            android:theme="@style/Theme.RescueApp.NoActionBar" />
        
        <!-- GPS位置同步服务 -->
        <service
            android:name=".service.LocationSyncService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="location" />
        
        <!-- 任务通知服务 -->
        <service
            android:name=".service.MissionNotificationService"
            android:enabled="true"
            android:exported="false" />
        
        <!-- 位置更新接收器 -->
        <receiver
            android:name=".receiver.LocationUpdateReceiver"
            android:enabled="true"
            android:exported="false" />

    </application>

</manifest>
