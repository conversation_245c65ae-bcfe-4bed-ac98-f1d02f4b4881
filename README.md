# 救援应用 (RescueApp)

这是一个基于Android的救援应用，支持GPS位置同步、任务管理、队友位置展示等功能。

## 功能特性

### 安卓App功能
- ✅ **当前位置GPS展示** - 在地图上展示自己兵牌所在位置
- ✅ **队友GPS展示** - 在地图上展示附近队友兵牌位置  
- ✅ **接收/执行搜救任务** - 接收来自桌面大屏的搜救任务，并执行搜救任务
- ✅ **发起搜救任务** - 当需要救援时可以发起搜救任务
- ✅ **登录/登出** - 用户认证功能
- ✅ **自动位置同步模式** - 按照设置间隔时间自动向系统同步GPS坐标信息
- ✅ **手动位置同步模式** - 手动点击同步之后才会向系统同步GPS坐标信息
- ✅ **历史行动轨迹** - 展示一定时间内自己的行动轨迹

## 技术架构

### 开发环境
- **开发工具**: PyCharm (代码编写) + Android Studio (运行测试)
- **编程语言**: Kotlin
- **最低SDK**: Android 7.0 (API 24)
- **目标SDK**: Android 14 (API 34)

### 核心技术栈
- **架构模式**: MVVM + Repository Pattern
- **UI框架**: Material Design 3
- **地图服务**: Google Maps SDK
- **位置服务**: Google Play Services Location
- **数据库**: Room Database
- **网络请求**: Retrofit + OkHttp
- **异步处理**: Kotlin Coroutines + Flow
- **依赖注入**: 手动依赖注入

### 项目结构
```
app/src/main/java/com/rescue/app/
├── data/                    # 数据层
│   ├── local/              # 本地数据库
│   ├── remote/             # 网络API
│   └── repository/         # 数据仓库
├── domain/                 # 领域层
│   └── model/              # 数据模型
├── service/                # 后台服务
├── ui/                     # UI层
│   ├── login/              # 登录模块
│   ├── main/               # 主界面
│   ├── map/                # 地图模块
│   ├── mission/            # 任务模块
│   └── profile/            # 个人中心
└── utils/                  # 工具类
```

## 快速开始

### 1. 环境准备
1. 安装Android Studio
2. 安装PyCharm (可选，用于代码编写)
3. 配置Android SDK (API 24-34)
4. 获取Google Maps API Key

### 2. 项目配置
1. 克隆项目到本地
2. 在`app/src/main/AndroidManifest.xml`中替换Google Maps API Key:
   ```xml
   <meta-data
       android:name="com.google.android.geo.API_KEY"
       android:value="YOUR_GOOGLE_MAPS_API_KEY" />
   ```
3. 在`ApiService.kt`中配置后端服务器地址:
   ```kotlin
   private const val BASE_URL = "http://your-server-url/api/"
   ```

### 3. 编译运行
1. 在Android Studio中打开项目
2. 等待Gradle同步完成
3. 连接Android设备或启动模拟器
4. 点击运行按钮

### 4. 权限配置
应用需要以下权限：
- 位置权限 (ACCESS_FINE_LOCATION, ACCESS_COARSE_LOCATION)
- 后台位置权限 (ACCESS_BACKGROUND_LOCATION, Android 10+)
- 网络权限 (INTERNET, ACCESS_NETWORK_STATE)
- 前台服务权限 (FOREGROUND_SERVICE)

## 主要功能说明

### 地图功能
- 显示当前位置和队友位置
- 支持自动/手动位置同步模式
- 历史轨迹显示
- 地图缩放和拖拽

### 任务管理
- 任务列表展示
- 任务状态筛选
- 接收和执行任务
- 任务状态更新

### 位置同步服务
- 后台GPS位置获取
- 自动/手动同步模式
- 位置数据本地存储
- 网络同步功能

## 开发指南

### 添加新功能
1. 在对应的模块下创建新的Fragment/Activity
2. 创建对应的ViewModel
3. 更新导航图和菜单
4. 添加必要的权限和配置

### 数据库操作
使用Room数据库进行本地数据存储：
```kotlin
// 插入位置数据
val locationData = LocationData(...)
locationRepository.insertLocation(locationData)

// 查询位置历史
locationRepository.getLocationsByUser(userId).observe(this) { locations ->
    // 处理位置数据
}
```

### 网络请求
使用Repository模式进行网络请求：
```kotlin
// 同步位置数据
viewModel.syncLocations().collect { resource ->
    when (resource) {
        is Resource.Success -> // 处理成功
        is Resource.Error -> // 处理错误
        is Resource.Loading -> // 显示加载状态
    }
}
```

## 注意事项

1. **Google Maps API Key**: 需要在Google Cloud Console中启用Maps SDK for Android
2. **位置权限**: Android 10+需要额外申请后台位置权限
3. **后台服务**: 位置同步服务会在后台持续运行
4. **网络配置**: 需要配置正确的后端服务器地址
5. **测试环境**: 建议在真实设备上测试GPS功能

## 后续开发计划

- [ ] 完善任务创建功能
- [ ] 添加消息推送功能
- [ ] 实现离线地图支持
- [ ] 添加语音通信功能
- [ ] 优化电池使用
- [ ] 添加数据统计功能

## 联系方式

如有问题或建议，请联系开发团队。
